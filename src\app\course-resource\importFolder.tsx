"use client";
import React, { useEffect, useState } from "react";
import type {
  <PERSON>mboData,
  ErrorCatch,
  ImportFolderRequest,
  ResourceDataRequest,
  ResourceList,
  FolderResourecList,
  ToastType,
  LogUserActivityRequest,
} from "@/types";
import { Button } from "@/components/ui/button";
import { DataTable } from "primereact/datatable";
import { Combobox } from "@/components/ui/combobox";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { useToast } from "@/components/ui/use-toast";
import { Spinner } from "@/components/ui/progressiveLoader";
import useCourse from "@/hooks/useCourse";
import { Column } from "primereact/column";
import moment from "moment";
import "../../styles/main.css";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function ImportFolder({
  onCancel,
  onSave,
  courseId,
  sectionsId,

  moduleOrders,
}: {
  onSave: () => void;
  onCancel: () => void;
  courseId?: string;
  sectionsId?: string;
  is_premium?: string;
  moduleOrders?: number[];
}): React.JSX.Element {
  const { t } = useTranslation();
  const [resourceData, setResourceData] = useState<FolderResourecList[]>([]);
  const [selectedResourceData, setSelectedResourceData] = useState<
    FolderResourecList[]
  >([]);

  const { importFolderToCourse } = useResourceLibrary();
  const { listFolderFromLibrary, getFolderResources } = useCourse();
  const { updateUserActivity } = useLogUserActivity();
  const { toast } = useToast() as ToastType;
  const existingOrders: number[] = [];
  const [comboData, setComboData] = useState<ComboData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [defaultLabel, setDefaultLabel] = useState<string>("");
  const [defaultOrder, setDefaultOrder] = useState<number>(1);

  useEffect(() => {
    const maxOrder = Math.max(...(moduleOrders ?? []));
    if (maxOrder > 0) {
      setDefaultOrder(maxOrder + 1);
    }
    void getFoldersList();
  }, []);

  const getFoldersList = async (): Promise<void> => {
    try {
      const orgId = localStorage.getItem("orgId");
      const folderList = await listFolderFromLibrary(orgId as string);
      const comboData: ComboData[] = folderList
        ?.map((cat) => ({
          value: cat.folder_id,
          label: cat.folder_name,
        }))
        .sort((a, b) => a.label.localeCompare(b.label));
      setComboData(comboData);
      setDefaultLabel(comboData[0]?.label as string);
      void getResourceList(comboData[0]?.value as string);
    } catch (error) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.error_fetching_data"),
      });
    }
  };

  const getResourceList = async (folderId: string): Promise<void> => {
    try {
      setIsLoading(true);
      const folderList = await getFolderResources(
        folderId as string,
        courseId as string,
        sectionsId as string,
      );
      setIsLoading(false);
      const maxOrder = Math.max(...(moduleOrders ?? []));
      const nextOrder = maxOrder > 0 ? maxOrder + 1 : 1;
      if (folderList != null) {
        const filteredList = folderList
          .filter((item: FolderResourecList) => !item.is_linked)
          .map((item, index) => ({
            ...item,
            module_type:
              item.module_type === "url" ? "video" : item.module_type,
            order: nextOrder + index,
          }));
        setResourceData(filteredList);
      } else {
        setResourceData([]);
      }
    } catch (error) {
      setIsLoading(false);
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.error_fetching_data"),
      });
    }
  };

  useEffect(() => {
    setSelectedResourceData(resourceData);
  }, [resourceData]);

  const handleCancel = (): void => {
    onCancel();
  };

  const updateLogUserActivity = async (
    params: LogUserActivityRequest,
  ): Promise<void> => {
    const response = await updateUserActivity(params);
    console.log("response", response);
  };

  const importResource = (): void => {
    if (selectedResourceData.length === 0) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description: t("errorMessages.select_one_folder"),
      });
    } else {
      const orderValues: number[] = [];
      const resourcesWithoutOrder = selectedResourceData.filter(
        (item) =>
          item.order === undefined || item.order === null || item.order === 0,
      );

      if (resourcesWithoutOrder.length > 0) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.order_mandatory_alert"),
        });
        return;
      }
      const hasDuplicates = selectedResourceData.some((item) => {
        if (moduleOrders?.includes(item.order as number) ?? false) {
          existingOrders.push(item.order as number);
        }
        if (orderValues.includes(item.order as number)) {
          return true;
        }
        orderValues.push(item.order as number);
        return false;
      });

      if (existingOrders.length > 0) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: `${t("errorMessages.order_exist")} ${existingOrders.join(
            ", ",
          )}`,
        });
        return;
      } else if (hasDuplicates) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.duplicate_resource_alert"),
        });
        return;
      } else {
        const resources: ResourceDataRequest[] = [];
        selectedResourceData.forEach((item) => {
          resources.push({
            module_id: item.module_id,
            resource_id: item.resource_id as string,
            module_order: item.order as number,
            
          });
        });

        const reqParams: ImportFolderRequest = {
          resource_data: resources,
          org_id: localStorage.getItem("orgId") as string,
          course_id: courseId as string,
          section_id: sectionsId as string,
        };

        const fetchData = async (): Promise<void> => {
          try {
            const response = await importFolderToCourse(reqParams);
            if (response.status === "success") {
              toast({
                variant: SUCCESS_MESSAGES.toast_variant_default,
                title: t("successMessages.toast_success_title"),
                description: t("successMessages.import_folder"),
              });
              const params = {
                activity_type: "Course",
                screen_name: "Course",
                action_details: "Folder imported successfully",
                target_id: courseId as string,
                log_result: "SUCCESS",
              };
              void updateLogUserActivity(params).catch((error) => {
                console.error(error);
              });
              onSave();
            } else {
              const params = {
                activity_type: "Course",
                screen_name: "Course",
                action_details: "Failed to import folder",
                target_id: courseId as string,
                log_result: "ERROR",
              };
              void updateLogUserActivity(params).catch((error) => {
                console.error(error);
              });
            }
          } catch (error) {
            const err = error as ErrorCatch;
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: err?.message,
            });
          }
        };
        fetchData().catch((error) => console.log(error));
      }
    }
  };

  const handleFolderChange = (value: string): void => {
    void getResourceList(value);
  };

  const handleSelectionChange = (e: { value: FolderResourecList[] }): void => {
    console.log(e.value);
    setSelectedResourceData(e.value);
  };

  return (
    <div className="border rounded-md p-4 mt-4">
      <div className="w-full flex justify-between space-x-4">
        <div className="w-full flex items-center"></div>
      </div>
      <div className="flex md:mr-auto items-center mb-5">
        <div className="flex md:mr-auto items-center">
          <div className="md:min-w-[430px]">
            <Combobox
              data={comboData}
              onSelectChange={handleFolderChange}
              placeHolder={String(t("courses.courseModule.selectFolder"))}
              defaultLabel={defaultLabel}
            />
          </div>
        </div>
      </div>
      {isLoading ? (
        <Spinner />
      ) : (
        <div>
          <DataTable
            value={resourceData}
            selection={selectedResourceData}
            onSelectionChange={handleSelectionChange}
            dataKey="resource_id"
            paginator
            rows={10}
            selectionMode="multiple"
            className="p-datatable-striped border border-gray-300 rounded-lg bg-gray"
          >
            <Column
              selectionMode="multiple"
              headerStyle={{
                width: "2.5em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              className="text-sm text-gray-700 border-b border-gray-300 bg-white"
              bodyClassName="p-0.5 pl-2"
            />
            <Column
              field="index"
              header={String(t("courses.courseModule.slno"))}
              headerStyle={{
                width: "5em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              body={(data, options) => options.rowIndex + 1}
              className=" text-sm text-gray-700 border-b border-gray-300 p-0.5 bg-white"
            />
            <Column
              field="resource_name"
              header={String(t("courses.courseModule.resourceName"))}
              headerStyle={{
                width: "12em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              className=" text-sm text-gray-700 border-b border-gray-300 p-0.5 bg-white"
            />
            <Column
              field="module_type"
              header={String(t("courses.courseModule.moduleType"))}
              headerStyle={{
                width: "12em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              className=" text-sm text-gray-700 border-b border-gray-300 p-0.5 bg-white"
            />
            <Column
              header={String(t("courses.courseModule.createdOn"))}
              headerStyle={{
                width: "10em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              body={(rowData: ResourceList) =>
                moment
                  .utc(rowData.created_at)
                  .local()
                  .format("DD-MMM-YYYY hh:mm a")
              }
              className=" text-sm text-gray-600 border-b border-gray-300 p-0.5 bg-white"
            />
            <Column
              header={String(t("courses.courseModule.order"))}
              headerStyle={{
                width: "8em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              body={(rowData: FolderResourecList, options) => (
                <input
                  type="number"
                  min="0"
                  defaultValue={defaultOrder + options.rowIndex}
                  onChange={(e) => {
                    const value = Math.max(0, Number(e.target.value));
                    rowData.order = value;
                  }}
                  className="w-12 border border-gray-300 p-0.5 rounded-sm text-red"
                  disabled={
                    !selectedResourceData.some(
                      (selected) =>
                        selected.resource_id === rowData.resource_id,
                    )
                  }
                />
              )}
              className="border-b border-gray-300 p-0.5 bg-white"
            />
          </DataTable>
        </div>
      )}
      <div className="flex flex-wrap justify-end mt-4">
        <div className="sm:px-2 md:px-3 lg:px-4 xl:px-5 2xl:px-6">
          <Button
            variant="outline"
            className="w-full sm:w-auto"
            onClick={handleCancel}
          >
            {String(t("buttons.cancel"))}
          </Button>
        </div>
        <div>
          <Button onClick={importResource}>
            {String(t("buttons.importFolder"))}
          </Button>
        </div>
      </div>
    </div>
  );
}
