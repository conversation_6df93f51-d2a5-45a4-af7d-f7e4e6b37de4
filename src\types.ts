import type { z } from "zod";
import type {
  FormSchema,
  ResetPasswordSchema,
  LoginFormSchema,
  AddExamSchema,
  AddCourseSchema,
  AddResourceschema,
  EditCourseSchema,
  CheckPointFormShema,
  AddPageContentSchema,
  AddCurrentAffairsSchema,
  AddCourseVideoschema,
  AddQuestionCategoryschema,
  UpdateVideoCheckPointSchema,
  UpdateGroupschema,
  MembershipPlanSchema,
  UpdateTopicschema,
  AddPageResourceSchema,
  UpdateFolderSchema,
  UpdatePPTCheckPointSchema,
  PPTCheckPointFormShema,
  NotificationSchema,
  EmailSchema,
  addFoldersSchema,
  LiveClassSchema,
} from "./schema/schema";
import type { FieldError } from "react-hook-form";
import type { Column, Row } from "@tanstack/react-table";
import type { MomentInput } from "moment";
import type moment from "moment";
import type { ToastActionElement, ToastProps } from "@/components/ui/toast";
import { type LucideIcon } from "lucide-react";

export type UpdateVideoCheckPoint = z.infer<typeof UpdateVideoCheckPointSchema>;
export type UpdatePPTCheckPoint = z.infer<typeof UpdatePPTCheckPointSchema>;
export type FormSchemaType = z.infer<typeof FormSchema>;
export type PasswordSchemaType = z.infer<typeof ResetPasswordSchema>;
export type AddExamSchemaType = z.infer<typeof AddExamSchema>;
export type AddCourseSchemaType = z.infer<typeof AddCourseSchema>;
export type EditCourseSchemaType = z.infer<typeof EditCourseSchema>;
export type AddResources = z.infer<typeof AddResourceschema>;
export type CheckPointForm = z.infer<typeof CheckPointFormShema>;
export type PPTCheckPointForm = z.infer<typeof PPTCheckPointFormShema>;
export type PageContentForm = z.infer<typeof AddPageContentSchema>;
export type PageResourceForm = z.infer<typeof AddPageResourceSchema>;
export type AddCourseVideoForm = z.infer<typeof AddCourseVideoschema>;
export type AddQuestionCategoryForm = z.infer<typeof AddQuestionCategoryschema>;
export type AddCurrentAffairsSchemaType = z.infer<
  typeof AddCurrentAffairsSchema
>;
export type updateGroupForm = z.infer<typeof UpdateGroupschema>;
export type updateFolderForm = z.infer<typeof UpdateFolderSchema>;
export type addFolderForm = z.infer<typeof addFoldersSchema>;
export type MembershipPlanSchemaType = z.infer<typeof MembershipPlanSchema>;
export type MembershipPlanEditModalSchema = z.infer<
  typeof MembershipPlanSchema
>;
export type updateTopicForm = z.infer<typeof UpdateTopicschema>;
export type NotificationSchema = z.infer<typeof NotificationSchema>;
export type EmailSchema = z.infer<typeof EmailSchema>;
export type LiveClassSchemaType = z.infer<typeof LiveClassSchema>;
export interface FormFieldResult {
  invalid: boolean;
  isDirty: boolean;
  isTouched: boolean;
  error?: FieldError | undefined;
  id: string;
  name: string;
  formItemId: string;
  formDescriptionId: string;
  formMessageId: string;
}

export interface PaginationProps {
  visiblePages: number;
  data: unknown[];
  onPostsToDisplayChange: (newPostsToDisplay: unknown) => void;
  searchStatus?: boolean;
  onCurrentPageChange?: (currentPage: number) => void;
}

export interface CoursesParams {
  is_premium?: boolean;
  id: string;
  course_id?: string;
  org_id?: string;
  category_id?: string;
  category_name: string;
  category_publish_status: string;
  short_name: string;
  full_name?: string;
  duration: string;
  status: string;
  description: string;
  format: string;
  start_date: moment.MomentInput;
  end_date: moment.MomentInput;
  is_expired?: boolean;
  is_user_enrolled: boolean;
  is_duplicate_course?: boolean;
  no_of_enrollments: number;
  parent_course_id: string;
  course_type: string;
  visibility: boolean;
}
[];

export interface ModalProps {
  title: string;
  children: React.JSX.Element;
  header: string;
  openDialog: boolean;
  closeDialog: (value: boolean) => void;
  footer?: React.ReactNode;
  type?: string;
}

export interface CourseDeatilsType {
  category_id: string;
  course_id: string;
  course_resources: [
    {
      id: string;
      name: string;
      type: string;
      url: string;
      created_at: string;
      updated_at: string;
      org_id: string;
    },
  ];
  current_affairs: [
    {
      content: string;
      course_id: string;
      id: string;
      img_url: string;
      org_id: string;
      publish_date: string;
      row_num: number;
      title: string;
      type: string;
    },
  ];
  end_date: string;
  full_name: string;
  modules: [
    {
      course_id: string;
      instance: string;
      module_id: string;
      module_name: string;
      module_source: string;
      module_type: string;
      section_id: string;
    },
  ];
  sections: [
    {
      course_id: string;
      name: string;
      section_id: string;
      section_order: number;
      summary: string;
    },
  ];
  short_name: string;
  start_date: string;
  status: string;
  summary: string;
}

// export type AddQuestionSchemaType = {
//   category: string;
//   questiontype: string;
//   type: string;
//   markassigned: string;
//   penality: string;
//   answernumber: number;
//   answers?: {
//     isAnswer: boolean;
//     type: string;
//     text: string;
//     fraction: string;
//   }[];
// };

export interface AddQuestionSchemaType {
  question: richTextType | string;
  category?: string;
  questiontype?: string;
  contenttype?: string;
  type?: string;
  markassigned?: number;
  penality?: number;
  answernumber?: number;
  answers?: {
    slot?: number;
    id?: string;
    isAnswer: boolean;
    type: string;
    text: string;
    fraction: number;
  }[];
}

export interface EnrollmentDataType {
  enrolled_date?: string;
  first_name?: string;
  id?: string;
  last_name?: string;
}

export interface ExamInfoType {
  quiz_id: string;
  org_id: string;
  question_with_options: string;
  response_summary: string;
  course_id: string;
  course_name: string;
  quiz_name: string;
  quiz_start_time: string;
  quiz_end_time: string;
  duration: string;
  quiz_attempt_id: string;
  right_answer: boolean;
}

export interface ExamType {
  topic: string;
  courses: string;
  section: string;
  type: string;
  exam_name: string;
  no_of_questions: number;
  total_marks: number;
  pass_mark: number;
  duration: string;
  attempts: number;
  valid_from: string;
  valid_to: string;
}

export interface ExamDataType {
  id: string;
  name: string;
  org_id: string;
  duration: number;
  end_time: string;
  course_id: string;
  section_id: string;
  pass_mark: number;
  quiz_type: string;
  created_at: string;
  is_premium: boolean;
  main_topic: string;
  start_time: string;
  total_mark: number;
  updated_at: string;
  description: string;
  allowed_attempts: number;
  num_of_questions: number;
  penalty_available: boolean;
  is_equal_weightage: boolean;
  eq_weightage_marks: number;
  publish_status: string;
}

export interface ExamDetailsData {
  name: string;
  start_time: string;
  end_time: string;
  num_of_questions: number;
  total_mark: number;
  pass_mark: number;
  description: string;
}

export interface AddCategoryInCourse {
  name: string;
  description: string;
  is_premium?: boolean;
  topic: string;
  category_type: string;
}
export interface AddNewExamFormType {
  course_id: string;
  section_id: string;
  name?: string;
  description: richTextType | string;
  main_topic: string;
  num_of_questions: number;
  total_mark: number;
  pass_mark: number;
  // start_time: string;
  start_time: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  end_time: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  // end_time: string;
  duration: number;
  allowed_attempts: number;
  quiz_type?: string;
  penalty_available: boolean;
  is_premium?: boolean;
  marks?: string;
  is_weightage?: boolean;
  folder_id?: string;
  penalty_type?: string;
  penalty_mode?: string;
}
export interface AddExamResourceForm {
  name?: string;
  description: richTextType | string;
  main_topic: string;
  num_of_questions: number;
  total_mark: number;
  pass_mark: number;
  duration: number;
  allowed_attempts: number;
  quiz_type?: string;
  penalty_available: boolean;
  is_premium?: boolean;
  marks?: string;
  is_weightage?: boolean;
  folder_id?: string;
  penalty_type: string;
  penalty_mode: string;
}
export interface EditNewExamFormType {
  course_id: string;
  name?: string;
  description: richTextType | string;
  num_of_questions: number;
  total_mark: number;
  pass_mark: number;
  // start_time: string;
  start_time: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  end_time: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  // end_time: string;
  duration: number;
  allowed_attempts: number;
  quiz_type?: string;
  penalty_available: boolean;
  is_premium?: boolean;
  marks?: string;
  is_weightage?: boolean;
  penalty_type?: string;
  penalty_mode?: string;
}

export interface ExamTimeType {
  start_time: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  end_time: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
}

export interface AddNewExamSubmitType {
  course_id: string;
  section_id: string;
  org_id: string;
  quiz_data: {
    quiz_id?: string | null;
    name?: string;
    description: richTextType | string;
    main_topic: string;
    num_of_questions: number;
    total_mark: number;
    pass_mark: number;
    start_time: string;
    end_time: string;
    duration: number;
    allowed_attempts: number;
    quiz_type?: string;
    penalty_available: boolean;
    is_premium?: boolean;
    no_wrong_answers?: number;
    minus_mark_applicable?: number;
  };
}
export interface updateExamSubmitType {
  org_id: string;
  quiz_data: {
    id?: string | null;
    name?: string;
    description: richTextType | string;
    main_topic: string;
    num_of_questions: number;
    total_mark: number;
    pass_mark: number;
    start_time: string;
    end_time: string;
    duration: number;
    allowed_attempts: number;
    quiz_type?: string;
    penalty_available: boolean;
    is_premium?: boolean;
  };
}

export interface UsersDataType {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  bio: string;
  avatar_url: string;
  phonenumber1: number;
  phonenumber2: number;
  org_id: string;
  org_name: string;
  roles: string[];
  status: string;
  hideViews: boolean;
  define_heirarchy?: boolean;
  hideRoleChange?: boolean;
}

export interface AttendedExamsType {
  user_id: string;
  org_id: string;
  email: string;
  first_name: string;
  last_name: string;
  course_id: string;
  course_name: string;
  course_start_date: string;
  course_end_date: string;
  name: string;
  attempt: number;
  quiz_start_time: string;
  quiz_end_time: string;
  duration: number;
  quiz_attempt_id: string;
  quiz_total_marks: number;
  quiz_pass_mark: number;
  quiz_status: string;
  result_of_quiz: string;
  quiz_type: string;
}

export interface RoleType {
  id: string;
  name: string;
  display_name?: string;
}

export type ToasterToast = ToastProps & {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
};

export interface ToasterReturnType {
  id: string;
  dismiss: () => void;
  update: (props: ToasterToast) => void;
}
export type Toast = Omit<ToasterToast, "id">;
export interface UseToast {
  toast: ({ ...props }: Toast) => ToasterReturnType;
  dismiss: (toastId?: string) => void;
  toasts: ToasterToast[];
}

export interface ExamInfo {
  quiz_id: string;
  org_id: string;
  question_with_options: string;
  response_summary: string;
  course_id: string;
  course_name: string;
  quiz_name: string;
  quiz_start_time: string;
  quiz_end_time: string;
  duration: string;
  quiz_attempt_id: string;
}

export interface CourseResource {
  id: string;
  name: string;
  url: string;
}

export interface CourseModule {
  id: string;
  module_name: string;
}

export type CallbackType = (data: []) => void;
export type CallbackType2 = (data: QuestionBankData[] | CourseModule[]) => void;

export interface QuestionBankData {
  question_id?: string;
  org_id?: string;
  name?: string;
  question_text?: string;
  default_mark?: number;
  penalty?: number;
  question_category_id?: string;
  status?: string;
  question_type?: string;
  question_category_name?: string;
  hideIcon?: boolean;
  hideEdit?: boolean;
  question_publish_status?: string;
  answers?: [
    {
      id: string;
      slot: number;
      answer: string;
      org_id: string;
      fraction: number;
      ans_format: string;
      created_at: string;
      updated_at: string;
      answer_type: string;
      question_id: string;
    },
  ];
}

export interface ExamDetailsType {
  name?: string;
  duration?: number;
  answers?: {
    slot: number;
    answer: string;
    org_id: string;
    fraction: number;
    answer_id: string;
    ans_format: string;
    created_at: string;
    updated_at: string;
    answer_type: string;
    question_id: string;
    ansMarked?: boolean;
  }[];
  penalty?: number;
  quiz_id?: string;
  question_id?: string;
  default_mark?: number;
  question_slot?: number;
  question_text?: string;
  question_type?: string;
}

export interface SectionType {
  section_id: string;
  course_id: string;
  name: string;
  summary: string;
  section_order: number;
}
export interface ComboData {
  value?: string;
  label?: string;
  isDisabled?: boolean;
}
export interface BatchQuestionData {
  question_text?: string;
  question_id?: string;
  question_category_id?: string;
  question_category_name?: string;
}
export interface CoursePublishDraftFormType {
  closeDialog: (value: boolean) => void;
  data?: string;
  status?: string;
  onSave?: () => void;
}

export interface modalButtonType {
  closeLabel: string;
  submitLabel?: string;
  closeDialog: (value: boolean) => void;
}

export interface CourseDuplicateFormType {
  closeDialog: (value: boolean) => void;
}
export interface CourseDuplicateType {
  //data: any;
  //nodeData: TreeNode[];
  onSave: () => void;
  closeDialog: (value: boolean) => void;
  data?: CoursesParams;

  isDialogOpen: (value: boolean) => void;
  nodeData?: CourseDuplicateNodeDataType;
  topicList?: TreeDataItem[];
}
export interface publicURL {
  data: {
    publicUrl: string;
  };
}

interface TreeDataItem {
  data: string | null;
  key?: string;
  value: string;
  label: string;
  description?: string;
  icon?: LucideIcon;
  children?: TreeDataItem[] | null;
  is_parent?: boolean;
}
export interface CourseDuplicateNodeDataType {
  data: string;
  key: string;
  label: string;
}
[];

export interface CourseDuplicateTypeData {
  course_id: string;
  org_id: string;
  category_id: string;
  category_name: string;
  short_name: string;
  full_name: string;
  start_date: MomentInput;
  end_date: MomentInput;
  duration: string;
  status: string;
  course_type: string;
  courseVisibility: string;
}

export interface CourseResourceAddFormType {
  closeDialog: (value: boolean) => void;
}

export interface ResourceDataType {
  data: { StepType: string; StepLabel: string; StepInstance: string };
  onSteptypeChange: (value: string) => void;
}
export interface fileDataType {
  url: string;
}
export type LoginFormType = z.infer<typeof LoginFormSchema>;
export interface TopicDataType {
  key?: string | number;
  value: string;
  label: string;
  description: string;
  org_id?: string;
  children?: TopicDataType[] | null;
  is_premium?: boolean;
  publish_status?: string;
  hideIcon?: boolean;
  hideEditDelete?: boolean;
  hideEdit?: boolean;
}
export interface SessionData {
  access_token: string;
  expires_at: number;
  expires_in: number;
  refresh_token: string;
  token_type: string;
}
export interface ToastType {
  toast: (options: {
    variant?: string;
    title: string;
    description?: string;
  }) => void;
}
export interface ErrorType {
  code: string;
  details: string;
  hint: string;
  message: string;
}

export interface CourseParams {
  format: string;
  org_id: string;
  category_id: string;
  category_name: string;
  course_id: string;
  duration: string;
  end_date: Date;
  full_name: string;
  short_name: string;
  start_date: Date;
  status: string;
  created_at?: Date;
  is_expired?: boolean;
  is_premium?: boolean;
  visibility?: boolean;
  course_type?: string;
  is_user_enrolled?: boolean;
  parent_course_id?: string;
  no_of_enrollments?: number;
  is_duplicate_course?: boolean;
  category_publish_status?: string;
}
export interface richTextEditorType {
  initialValue?: [];
  setRichTextValue?: [];
}
export interface addCourseFormType {
  courseDescription: richTextType | string;
  courseVisibility: string;
  courseCategory: string | null;
  courseFormat: string;
  courseShortName: string;
  courseFullName: string;
  courseEndDate: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  courseStartDate: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  numberOfSections: number | string;
  type: string;
}
export interface richTextType {
  htmlValue: string | undefined;
}
export interface CourseDuplicateFormType {
  courseCategory: string;
  courseShortName: string;
  courseFullName: string;
  course_type: string;
  courseVisibility: string;
  courseEndDate: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
  };
  courseStartDate: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
  };
}
export interface addCourseValueType {
  category_id: string | null;
  end_date: MomentInput;
  format: string;
  full_name: string;
  org_id?: string;
  short_name: string;
  start_date: string;
  status: string;
  summary: richTextType | string;
  type: string;
  num_sections: number | string;
}
export interface DuplicateCourseValueType {
  org_id: string;
  course_data: {
    course_id?: string;
    full_name: string;
    short_name: string;
    category_id: string;
    course_end_date: MomentInput;
    course_start_date: MomentInput;
    course_type?: string;
    courseVisibility: string;
  };
  update_section: boolean;
  update_exams: boolean;
  update_resources: boolean;
}

export interface CourseDuplicateFormType {
  courseCategory: string;
  courseShortName: string;
  courseFullName: string;
  courseEndDate: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
  };
  courseStartDate: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
  };
}

//export type AddQuestionSchemaType = z.infer<typeof AddquestionFormSchema>;

export interface AddquestionFormType {
  questiontype?: string;
  contenttype?: string;
  question?: string;
  markassigned?: number;
  penality?: number;
  answers?: {
    text: string;
    type: string;
    isAnswer: boolean;
    fraction: number;
  }[];
}
export interface QuestionFormSubmitType {
  answer_datas?: {
    slot: number;
    answer: string;
    fraction: number;
    answer_type: string;
  }[];
  org_id: string;
  ques_category_id?: string;
  question_data: {
    name: richTextType | string;
    question_text: richTextType | string;
    default_mark: number;
    penalty: number;
    question_type: string;
  };
  quiz_id?: string | null;
}

export interface QuizesOfCourse {
  id: string;
  name: string;
  org_id: string;
  duration: number;
  end_time: string;
  course_id: string;
  pass_mark: number;
  quiz_type: string;
  created_at: string;
  is_premium: boolean;
  main_topic: string;
  start_time: string;
  total_mark: number;
  updated_at: string;
  description: string;
  allowed_attempts: number;
  num_of_questions: number;
  penalty_available: boolean;
  publish_status: string;
  questions_exists: number;
  hideIcon: boolean;
}

export interface ColumnDefinition {
  column: Column<AttendedExamsType>;
}
export interface RowDefinition {
  row: Row<AttendedExamsType>;
}

export interface TreeCombo {
  key: string;
  label: string;
  data: string;
  children?: TreeCombo[] | null;
}

export interface ErrorCatch {
  details: string | undefined;
  message: string;
  Error?: string;
}

export interface TreeNode {
  key: string;
  data: string;
  label: string;
  children?: TreeNode[];
}
export interface EnrollmentTableType {
  id: string;
  name: string;
  org_id: string;
  duration: number;
  end_time: string;
  course_id: string;
  pass_mark: number;
  quiz_type: string;
  created_at: string;
  is_premium: boolean;
  main_topic: string;
  start_time: string;
  total_mark: number;
  updated_at: string;
  description: string;
  allowed_attempts: number;
  num_of_questions: number;
  penalty_available: boolean;
  publish_status: string;
  questions_exists: number;
  hideIcon: boolean;
  hideEdit?: boolean;
  hideStart?: boolean;
}
export interface EnrollmentColumnDefinition {
  column: Column<EnrollmentTableType>;
}
export interface EnrollmentRowDefinition {
  row: Row<EnrollmentTableType>;
}
export interface ExamDetailsTableType {
  name: string;
  default_mark: number;
  penalty: number;
}
export interface ExamDetailColumnDefinition {
  column: Column<ExamDetailsType>;
}
export interface UserDetailsTableType {
  email: string;
  first_name: string;
  last_name: string;
  bio: string;
  avatar_url: string;
  phonenumber1: number;
  phonenumber2: number;
  org_id: string;
  org_name: string;
  roles: string[];
}
export interface UserDetailsColumnDefinition {
  column: Column<UserDetailsTableType>;
}

export interface UserDetailsRowDefinition {
  row: Row<UserDetailsTableType>;
}

export interface ToastData {
  id: number;
  title: string;
  description: string;
  action: React.JSX.Element;
}
export interface CourseResourcesAccumilator {
  id: string;
  name: string;
  type: string;
  url: string;
  created_at: string;
  updated_at: string;
  org_id: string;
}
export interface CourseModuleAccumilator {
  course_id?: string;
  module_id: string;
  instance: string;
  section_id: string;
  module_type: string;
  module_name: string;
  module_source: string;
}

export interface Enrollment {
  id: string;
  enrolled_date: string;
  first_name: string;
  last_name: string;
  email: string;
  attended: boolean;
}
export interface EnrollmentRequest {
  course_id: string;
  user_id: string;
  org_id?: string;
}
export interface AddCourseResultType {
  status: string;
  course_id: string;
}
export interface DuplicateCourseResultType {
  status: string;
  new_course_id: string;
}

export interface AddExamsResultType {
  status: string;
  quiz_id: string;
}

export interface AddQuestionApiResultType {
  status: string;
  question_id: string;
  question_bank_id: string;
}

export interface CourseDetailsParams {
  course_id: string;
  org_id?: string;
  category_id?: string;
  category_name: string;
  short_name: string;
  full_name?: string;
  duration: string;
  status: string;
  description: string;
  start_date: moment.MomentInput;
  end_date: moment.MomentInput;
}

export interface CourseDetailsValueType {
  full_name: string;
  short_name: string;
  start_date: MomentInput;
  end_date: MomentInput;
  summary: string;
  sections: SectionDetails[];
  course_resources: [];
  category_id: string;
}
export interface SectionDetails {
  name: string;
  summary: string;
  course_id: string;
  section_id: string;
  section_order: number;
}
[];
export interface CourseDetailsResultType {
  length: number;
  course_id: string;
  short_name: string;
  full_name: string;
  summary: string;
  status: string;
  start_date: MomentInput;
  end_date: MomentInput;
  course_resources: [];
  sections: [];
  modules: [];
  category_id: string;
  current_affairs: [];
}

export interface DeleteCourseRequest {
  course_id?: string;
  org_id?: string;
}

export interface CourseDeleteResponse {
  status: string;
}

export interface AddEnrollments {
  org_id: string;
  course_id: string;
}
export interface AddEnrollmentsResult {
  id: string;
  last_name: string;
  first_name: string;
  email: string;
}

export interface Question {
  question_id: string;
  org_id: string;
  name: string;
  question_text: string;
  default_mark: number;
  penalty: number;
  question_category_id: string;
  status: string;

  answers?: [
    {
      id: string;
      slot: number;
      answer: string;
      org_id: string;
      fraction: number;
      ans_format: string;
      created_at: string;
      updated_at: string;
      answer_type: string;
      question_id: string;
    },
  ];
}

export interface ImportQuestionResult {
  status: string;
  num_questions_inserted: number;
}

export interface ImportQuestionProps {
  closeDialog: (value: boolean) => void;
  questionType: string;
  examDetailsList: (value: boolean) => void;
  numberOfQuestions: number;
  rowCount: number;
  questions?: (string | undefined)[];
  isExamData: boolean;
}

export interface ImportQuestionRequest {
  org_id: string;
  question_ids: string[];
  quiz_id: string;
}

export interface ExamDetailsResult {
  quiz_id: string;
}

export interface ExamDetails {
  allowed_attempts: number;
  description: string;
  duration: number;
  end_time: string;
  main_topic: string;
  name: string;
  num_of_questions: number;
  pass_mark: number;
  section_id: string;
  category_id: string;
  section_name: string;
  quest_answers: {
    answers: {
      ans_format: string;
      answer: string;
      answer_id: string;
      answer_type: string;
      created_at: string;
      fraction: number;
      org_id: string;
      question_id: string;
      slot: number;
      updated_at: string;
    }[];
    default_mark: number;
    name: string;
    penalty: number;
    question_id: string;
    question_slot: number;
    question_text: string;
    question_type: string;
    quiz_id: string;
  }[];
  quiz_type: string;
  start_time: string;
  total_mark: number;
  is_equal_weightage: boolean;
  eq_weightage_marks: number;
  no_wrong_answers: number;
  minus_mark_applicable: number;
  penalty_available: boolean;
  calculation_type: string;
  penalty_type: string;
}

export interface ExtractExamDetails {
  // name: string;
  // default_mark: number;
  // penalty: number;
  question_text: string;
  answers: string[];
  answer_id: string;
}
export interface SectionItem {
  section_id: string;
  course_id: string;
  name: string;
  summary: string;
  section_order: number;
}

export interface SectionViewResultType {
  length: number;
  name: string;
  modules: ResoureceModuleType[];
  summary: string;
  course_id: string;
  resources: [];
  section_id: string;
  section_order: number;
}

export interface ViewResourcePageType {
  id: string;
  name: string;
  org_id: string;
  course_id: string;
  description: string;
  external_url: string;
  module_source: string;
  course_module_id: string;
  num_of_checkpoints: number;
  is_random_checkpoint: boolean;
  is_checkpoint_enabled: boolean;
  always_show_checkpoints: boolean;
  url: string;
  content: string;
  length: string;
  file_type: string;
}
export interface ResoureceModuleType {
  instance: string;
  progress: number;
  course_id: string;
  module_id: string;
  section_id: string;
  time_spent: string;
  module_name: string;
  module_type: string;
  module_source: string;
  marked_as_done: boolean;
  attempts_remaining: number;
  is_premium?: boolean;
  course_module_id: string;
  module_order: number;
}

export interface EditCourseFormType {
  courseDescription: richTextType | string;
  courseCategory: string | null;
  courseFullName: string;
  courseShortName: string;
  courseEndDate: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
}

export interface EditCourseValueType {
  course_data: {
    id: string | null;
    full_name: string;
    short_name: string;
    summary: richTextType | string;
    end_date: MomentInput;
    category_id: string;
  };
  org_id: string;
  section_datas: SectionData[];
  status: string;
}

export interface SectionData {
  id: string;
  summary: string;
  name: string;
}
export interface EditCourseResultType {
  status: string;
}

export interface LoginUserData {
  id: string;
  last_sign_in_at: string;
  email?: string;
  user_metadata: {
    first_name: string;
    last_name: string;
    phonenumber1: string;
  };
}
export interface ResourceFileRequest {
  org_id: string;
  course_id: string;
  section_id: string;
  file_data: {
    url: string;
    name: string;
    description: string;
    module_source: string;
    is_premium: boolean;
  };
}
export interface ResourceVideoRequest {
  org_id: string;
  course_id: string;
  section_id: string;
  url_data: {
    external_url: string;
    name: string;
    length: string;
    description: string;
    module_source: string;
    is_checkpoint_enabled?: string;
    num_of_checkpoints: number;
    is_random_checkpoint?: boolean;
    always_show_checkpoints?: boolean;
    is_premium: boolean;
  };
}
export interface ResourceFileResult {
  // export type AddQuestionSchemaType = {
  //   category: string;
  //   questiontype: string;
  //   type: string;
  //   markassigned: string;
  //   penality: string;
  //   answernumber: number;
  //   answers?: {
  //     isAnswer: boolean;
  //     type: string;
  //     text: string;
  //     fraction: string;
  //   }[];
  // };
  status: string;
  course_module_id: string;
}
export interface CheckpointQuizResponse {
  id: string;
  course_id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  org_id: string;
  main_topic: string;
  num_of_questions: number;
  total_mark: number;
  pass_mark: number;
  start_time: string;
  end_time: string;
  duration: number;
  penalty_available: string;
  allowed_attempts: number;
  quiz_type: string;
  is_premium: string;
}

export interface CheckPointList {
  sequence_number: string;
  checkpoint_name: string;
  checkpoint_startTime: {
    HH: string;
    MM: string;
    SS: string;
  };
  checkpoint_type: string;
  checkpoint_resid: string;
  checkpoint_reslabel?: string;
  isMandatory: string | undefined;
  checkpoint_resname?: string | undefined;
}
export interface PPTCheckPointList {
  sequence_number: string;
  checkpoint_name: string;
  checkpoint_slide: string;
  checkpoint_type: string;
  checkpoint_resid: string;
  checkpoint_reslabel?: string;
  isMandatory: string | undefined;
  checkpoint_resname?: string | undefined;
}

export interface CheckPointData {
  sequence: number;
  name: string;
  start_time: string;
  module_type_id: string;
  instance_id: string;
  checkpoint_type: string | undefined;
  is_mandatory: boolean | false;
}
export interface PPTCheckPointData {
  sequence: number;
  name: string;
  start_page: string;
  module_type_id: string;
  instance_id: string;
  checkpoint_type: string | undefined;
  is_mandatory: boolean | false;
}
export interface CheckPointRequest {
  checkpoint_data: CheckPointData[];
  course_module_id: string;
  org_id: string;
}
export interface PPTCheckPointRequest {
  checkpoint_data: PPTCheckPointData[];
  course_module_id: string;
  org_id: string;
}
export interface CheckPointResponse {
  status: string;
  details: string;
}

export interface UserData {
  id: string;
  first_name: string;
  last_name: string;
}

export interface AddUserEnrollment {
  course_id: string;
  org_id: string;
  users_ids: [];
}

export interface AddEnrollmentResponse {
  status: string;
}

export interface AddEnrollmentsFormType {
  course_id: string;
}
export interface AddPageForm {
  course_id: string;
  section_id: string;
  org_id: string;
  page_data: {
    name: string;
    content: string;
  };
}
export interface AddPageResponse {
  status: string;
}
export interface PageContentRequest {
  course_id: string;
  section_id: string;
  org_id: string;
  page_data: {
    name: string;
    content: string;
    is_premium: boolean;
  };
}
export interface SectionListResponse {
  course_id?: string;
  short_name?: string;
  full_name?: string;
  summary?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
  course_resources?: [];
  sections?: {
    section_id: string;
    course_id?: string;
    name: string;
    summary?: string;
    section_order?: string;
  }[];
  modules?: {}[];
  category_id?: string;
  current_affairs?: [];
}

export interface ValidSections {
  value: string;
  label: string;
}

export interface CurrentAffairsData {
  title: string;
  content: string;
  publish_date: string;
  id: string;
  publish_status: string;
  hideIcon: boolean;
  month?: string;
  created_at?: string;
  hideEditDelete?: boolean;
  hideEdit?: boolean;
}
export interface PublishAffair {
  bulletin_id?: string;
  org_id: string;
  status: string;
  publish_bulletin_date?: string;
}
export interface CurrentAffairsColumnDefinition {
  column: Column<CurrentAffairsData>;
}
export interface CurrentAffairsRowDefinition {
  row: Row<CurrentAffairsData>;
}
export interface CategoryRequest {
  org_id: string;
  category_data: {
    id?: string;
    name: string;
    description: string;
    parent_id?: string | null;
  };
}
export interface CategoryResponse {
  status: string;
}

export interface ExamResponse {
  status: string;
}
export interface EnrollementResponse {
  status: string;
}

export interface CategoryError {
  code: string;
  details: string;
  hint: null;
  message: string;
}
export interface SessionViewParams {
  course_id: string;
  course_module_id: string;
  user_id: string;
}
export interface SessionViewsResultType {
  status: string;
  session_data: SessionViews[];
}
export interface CourseModuleType {
  course_id?: string;
  module_id?: string;
  instance?: string;
  section_id: string;
  module_type: string;
  module_name?: string;
  module_source: string;
}

export interface SessionViews {
  modules: Module[];
  course_id: string;
  organisation: string;
  course_fullname: string;
  course_shortname: string;
}

export interface Module {
  checkpoints: Checkpoint[];
  resource_name: string;
  course_module_id: string;
}

export interface Checkpoint {
  sequence: number;
  sessions: Session[];
  start_time: string;
  instance_id: string;
  checkpoint_id: string;
  checkpoint_name: string;
}

export interface Session {
  result: string;
  user_id: string;
  lastname: string;
  quiz_name: string;
  first_name: string;
  instance_id: string;
  session_end_time: string;
  session_start_time: string;
  instance_attempt_id: string;
  session_attempt_number: number;
}
export interface UserSession {
  checkpoint_id: string;
  checkpoint_name: string;
  sequence: number;
  start_time: string;
  user_id: string;
  lastname: string;
  quiz_name: string;
  first_name: string;
  instance_id: string;
  session_end_time: string;
  session_start_time: string;
  instance_attempt_id: string;
  session_attempt_number: number;
  result: string;
}
export interface CourseModule {
  course_id: string;
  course_module_id: string;
  module_id: string;
  instance: string;
  section_id: string;
  module_type: string;
  module_name: string;
  module_source: string;
}

export interface LineChartProps {
  labels: string[];
  datasets: chartDataProps[];
}

export interface chartDataProps {
  label: string;
  data: number[];
  borderColor: string[];
  backgroundColor: string[];
  fill: boolean;
}
export interface CurrentAffairsResult {
  status: string;
  news_id: string;
}

export interface AddCurrentAffairs {
  bulletin_board: {
    content: string;
    title: string;
    type: string;
    publish_date: MomentInput;
    img_url: string;
    month: string;
  };
  course_id: null;
  org_id: string;
}
export interface CurrentAffairsForm {
  id?: string;
  title: string;
  content: string;
  publish_date: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  month: string;
}
export interface OrganizaionList {
  user_id: string;
  org_id: string;
  org_name: string;
}
export interface AddCourseVideoRequest {
  course_id: string;
  org_id: string;
  resource_data: {
    resource_name: string;
    resource_type: string;
    resource_url: string;
  };
}
export interface AddCourseVideoResponse {
  status: string;
}

interface DashboardCount {
  course_count?: number;
  exam_count?: number;
  group_count?: number;
  practice_count?: number;
  video_count?: number;
}

export interface DashboardStatsReturnType {
  status: string;
  dashboard_counts: DashboardCount;
}
interface MostWatchedUser {
  email: string;
  user_id: string;
  last_name: string;
  avatar_url: string;
  first_name: string;
  total_time_spent: string;
}

export interface MostWatchedUsersReturnType {
  status: string;
  result: MostWatchedUser[];
}
export interface CheckpointProgress {
  course_id: string;
  resource_id: string;
  resource_name: string;
  max_checkpoints: number;
  course_full_name: string;
  course_short_name: string;
  time_covered: string;
  percentage_covered: number;
  progressed_checkpoints: number;
}
export interface CheckpointsprogressReturnType {
  status: string;
  result: CheckpointProgress[];
}
interface CheckpointCount {
  name: string;
  video_id: string;
  course_id: string;
  course_full_name: string;
  check_point_count: number;
  course_short_name: string;
}
export interface CheckpointsCountReturnType {
  status: string;
  video_check_point: CheckpointCount[];
}

export interface CheckpointsCourseStats {
  course_id: string;
  course_full_name: string;
  course_short_name: string;
  failed_users: number;
  passed_users: number;
  pending_users: number;
  watched_users: number;
  enrolled_users: number;
  unwatched_users: number;
}

export interface CheckpointsCourseStatsRowDefinition {
  row: Row<CheckpointsCourseStats>;
}

export interface CheckpointsCourseStatsReturnType {
  labels: string[];
  datasets: [
    {
      data: number[];
      label: string;
      borderColor: string;
      backgroundColor: string;
      borderWidth: 2;
    },
  ];
}

export interface VideoStats {
  video_id?: string;
  video_name?: string;
  checkpoint_name?: string;
  exam_start_time?: string;
  last_watched_on?: string;
  checkpoint_result?: string;
}

export interface CheckpointsUserStatsReturnType {
  email: string;
  user_id: string;
  video_id: string;
  last_name: string;
  first_name: string;
  video_name: string;
  exam_result: string;
  checkpoint_name: string;
  enrollment_time: string;
  exam_start_time: string;
  last_watched_on: string;
  course_name?: string;
  progress_attened_on?: string;
  instance_progress?: string;
  sl_no?: number;
  user?: string;
}

export interface PublishCourseResponse {
  status: string;
}
export interface AddUserForm {
  reporting_to: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  phonenumber1: string;
  role?: string;
}

export interface AddUserRequest {
  email: string;
  password: string;
  data: {
    first_name: string;
    last_name: string;
    phonenumber1: string;
  };
}

export interface SignUpUserResponse {
  id: string;
  aud: string;
  role: string;
  email: string;
  phone: string;
  confirmation_sent_at: string;
  app_metadata: {
    provider: string;
    providers: [];
  };
  user_metadata: {
    first_name: string;
    last_name: string;
    phonenumber1: string;
  };
  identities: [
    {
      id: string;
      user_id: string;
      identity_data: {
        email: string;
        sub: string;
      };
      provider: string;
      last_sign_in_at: string;
      created_at: string;
      updated_at: string;
    },
  ];
  created_at: string;
  updated_at: string;
}

export interface SignUpResult {
  user?: {
    app_metadata: {
      first_name: string;
      last_name: string;
      phonenumber1: string;
    };
    aud: string;
    confirmation_sent_at: string;
    created_at: string;
    email: string;
    id: string;
    identities: [
      {
        id: string;
        user_id: string;
        identity_data: {
          email: string;
          sub: string;
        };
        provider: string;
        last_sign_in_at: string;
        created_at: string;
        updated_at: string;
      },
    ];
    phone: string;
    role: string;
    updated_at: string;
    user_metadata: {};
  } | null;
  session?: string | null;
}

export interface SignUpError {
  code: string;
  details: null;
  hint: null;
  message: string;
}

export interface AddUserProps {
  closeDialog: (value: boolean) => void;
  usersList: (value: boolean) => void;
  allUsers: UsersDataType[];
}

export interface UserUpdate {
  org_id: string;
  role_id: string;
  user_ids: string[];
}
export interface UserDeactivate {
  org_id: string;
  block: boolean;
  user_id: string;
}

export interface UserUpdateResult {
  status: string;
}
export interface UserDeactivateResult {
  status: string;
}

export interface UpdateUserParams {
  role_id: string;
  user_id: string;
  reporting_to?: string;
}

export interface AddUserResult {
  app_metadata: {
    provider: string;
    providers: string[];
  };
  aud: string;
  confirmation_sent_at: string;
  created_at: string;
  email: string;
  id: string;
  identities: {
    created_at: string;
    identity: string;
    identity_data: {
      email: string;
      sub: string;
    };
    last_sign_in_at: string;
    provider: string;
    updated_at: string;
    user_id: string;
  }[];
  phone: string;
  role: string;
  updated_at: string;
  user_metadata: {
    first_name: string;
    last_name: string;
    phonenumber1: string;
  };
}

export interface AddUserResponse {
  access_token: string;
  status?: string;
  token_type: string;
  expires_in: number;
  expires_at: number;
  refresh_token: string;
  user: {
    id: string;
    aud: string;
    role: string;
    email: string;
    email_confirmed_at: string;
    phone: string;
    last_sign_in_at: string;
    app_metadata: {
      provider: string;
      providers: [string];
    };
    user_metadata: {
      first_name: string;
      last_name: string;
      phonenumber1: string;
    };
    identities: [
      {
        identity_id: string;
        id: string;
        user_id: string;
        identity_data: {
          email: string;
          email_verified: boolean;
          phone_verified: boolean;
          sub: string;
        };
        provider: string;
        last_sign_in_at: string;
        created_at: string;
        updated_at: string;
        email: string;
      },
    ];
    created_at: string;
    updated_at: string;
  };
}

export interface UserSignUpError {
  code: number;
  msg: string;
}

export interface QuestionCategoryRequest {
  org_id: string;
  category_data: {
    name: string;
    description: string;
    parent_id: string | null;
  };
}
export interface QuestionCategory {
  closeDialog: (value: boolean) => void;
  isDialogOpen: (value: boolean) => void;
  categoryList: (value: boolean) => void;
  data: QuestionCategoryForm;
}
export interface QuestionCategoryResult {
  status: string;
  category_id: string;
}
export interface GroupForm {
  id?: string;
  name: string;
  description?: string;
}
export interface FolderForm {
  id?: string;
  name: string;
  description?: string;
}
export interface GroupFormDefinition {
  column: Column<GroupForm>;
}
export interface GroupFormRowDefinition {
  row: Row<GroupForm>;
}
export interface CourseGroup {
  id?: string;
  course?: string;
  end_date?: string;
  full_name?: string;
  is_part_of_group?: boolean;
  num_sections?: number;
  short_name?: string;
  start_date?: string;
  status?: string;
  summary?: string;
}
export interface CourseGroupColumnDefinition {
  column: Column<CourseGroup>;
}
export interface CourseGroupRowDefinition {
  row: Row<CourseGroup>;
}
export interface UserGroup {
  id?: string;
  user?: string;
  first_name?: string;
  last_name?: string;
  is_part_of_group?: boolean;
  email?: string;
  avatar_url?: string;
  user_ids?: string[];
}
export interface UserGroupColumnDefinition {
  column: Column<UserGroup>;
}
export interface UserGroupRowDefinition {
  row: Row<UserGroup>;
}

export interface UpdateCheckpointResponse {
  status: string;
}
export interface UpdateCheckPointRequest {
  checkpoint_data: {
    is_checkpoint_enabled: boolean;
    num_of_checkpoints: number;
    is_random_checkpoint: boolean;
    always_show_checkpoints?: boolean;
  };
  course_module_id?: string;
  instance_id: string;
  org_id: string;
}
export interface VideoModuleData {
  id: string;
  course_module_id: string;
  org_id: string;
  is_checkpoint_enabled: boolean;
}
export interface VideoCheckPointUpdateForm {
  closeDialog: (value: boolean) => void;
  // handleCheckpointData: (randomEnabled: boolean, cpNumber: number) => void;
  checkPointData: ViewResourcePageType;
  checkpointTime: number;
  onAddedValuesChange: (values: CheckPointForm[]) => void;
  allCheckPointData: CheckPointForm[];
  videoLength?: string | undefined;
  existingCheckPoints: CheckPointForm[];
}
export interface PPTCheckPointUpdateForm {
  closeDialog: (value: boolean) => void;
  // handleCheckpointData: (randomEnabled: boolean, cpNumber: number) => void;
  checkPointData: ViewResourcePageType;
  checkpointTime: number;
  onAddedValuesChange: (values: PPTCheckPointForm[]) => void;
  allCheckPointData: PPTCheckPointForm[];
  coursePPTModuleId?: string | undefined;
  existingCheckPoints: PPTCheckPointForm[];
}

export interface GroupRequest {
  org_id: string;
  group_id?: string;
  group_data?: {
    group_id?: string;
    group_name?: string;
    description?: string;
  };
}
export interface DeleteGroupRequest {
  group_id?: string;
}
export interface GroupResponse {
  status: string;
  group_id: string;
  users_data?: [];
  course_data?: [];
  privileges_data?: [];
}

export interface DeleteGroupResponse {
  status?: string;
  code?: string;
  details?: string;
  hint?: string;
  message?: string;
}

export interface SessionViewsTableType {
  result: string;
  user_id: string;
  lastname: string;
  quiz_name: number;
  first_name: string;
  instance_id: string;
  session_end_time: number;
  session_start_time: string;
  instance_attempt_id: string;
  session_attempt_number: boolean;
  email?: string;
}

export interface SessionViewsColumnDefinition {
  column: Column<SessionViewsTableType>;
}
export interface SessionViewsRowDefinition {
  row: Row<SessionViewsTableType>;
}
export interface CourseProgressedTableType {
  result: string;
  user_id: string;
  first_name: string;
  last_name: string;
  course_id: string;
  course_name: string;
  instance_id: string;
  instance_name: string;
  instance_progress: string;
  progress_attened_on: string;
  email: string;
}
export interface CourseProgressedColumnDefinition {
  column: Column<CourseProgressResult>;
}
export interface CourseProgressedRowDefinition {
  row: Row<CourseProgressResult>;
}
/* export interface UserSessionTableType {
  checkpoint_id: string;
  checkpoint_name: string;
  instance_id: string;
  sessions: SessionViewsTableType[];
  start_time: string;
} */
export interface UserSessionViewsColumnDefinition {
  column: Column<UserSession>;
}
export interface UserNotEnrolledViewsColumnDefinition {
  column: Column<UsersNotEnrolled>;
}
export interface UserNotEnrolledViewsRowDefinition {
  row: Row<UsersNotEnrolled>;
}
export interface UserSessionViewsRowDefinition {
  row: Row<UserSession>;
}

export interface AttendanceDataType {
  data: number[];
  label: string;
  borderColor: string;
  backgroundColor: string;
  borderWidth: number;
}

export interface CourseAttendancesGraphType {
  labels: string[];
  datasets: AttendanceDataType[];
}
export interface SessionStats {
  course_id?: string;
  course_name?: string;
  resource_id: string;
  resource_name: string;
  progressed_checkpoints: number;
  time_covered: string;
  percentage_covered: number;
}
export interface SessionStatsDataType {
  label: string;
  data: number[];
  backgroundColor: string[];
  borderWidth: number;
}
export interface SessionStatsGraphType {
  labels: string[];
  datasets: SessionStatsDataType[];
}
export type CallbackCheckPointsType = (data: []) => void;

export interface AssignUserList {
  created_at: string;
  email: string;
  first_name: string;
  id: string;
  last_name: string;
}

export interface AssignUserListColumnDefinition {
  column: Column<AssignUserList>;
}

export interface AssignOrganizationList {
  org_id: string;
  org_name: string;
  user_id: string;
}
export interface AssignOrganizationRequest {
  org_id: string;
  role_id: string;
  user_ids: string[];
}
export interface AssignOrganizationResult {
  status: string;
}

export interface AddUserToGroupType {
  org_id: string;
  group_id?: string;
  user_ids?: string[];
}
export interface ForgetPasswordForm {
  email: string;
}
export interface ResetPasswordReturn {
  code?: string;
  id?: string;
  error?: string;
  session?: {};
  msg?: string;
}
export interface AddCourseToGroupType {
  org_id: string;
  group_id?: string;
  course_ids?: string[];
}

export interface PrivilegeListForRole {
  privileges_data: {
    id: string;
    is_part_of_role: boolean;
    name: string;
    privilege_description: string;
    privilege_group_name: string;
    privilege_key: string;
  }[];
  status: string;
}

export interface AccessPrivilegeRequest {
  org_id: string;
  user_role_id: string | undefined;
  role_privileges: {
    privilege_id: string;
  }[];
}

export interface PrivilegeResult {
  status: string;
}

export interface GetPrivilegeRequest {
  org_id: string;
  role_id: string;
}
export interface GetPrivilegeListResponse {
  id: string;
  name: string;
  privilege_key: string;
  is_part_of_role: boolean;
  is_admin_privilege: boolean;
  privilege_group_name: string;
  privilege_description: string;
}
export interface GetPrivilegeResponse {
  org_id: string;
  status: string;
  role_id: string;
  role_name: string;
  role_privileges: [];
}
export interface PrivilegeRoleColumnDefinition {
  column: Column<GetPrivilegeResponse>;
}
export interface PrivilegeRoleRowDefinition {
  row: Row<GetPrivilegeResponse>;
}
export interface PrivilegeGroup {
  id?: string;
  name?: string;
  privilege_key?: string;
  is_part_of_group?: boolean;
  privilege_group_name?: string;
  privilege_description?: string;
}
export interface PrivilegeGroupColumnDefinition {
  column: Column<PrivilegeGroup>;
}
export interface PrivilegeGroupRowDefinition {
  row: Row<PrivilegeGroup>;
}
export interface AddPrivilegeToGroupType {
  org_id: string;
  group_id?: string;
  privilege_ids?: string[];
}
export interface startQuizAttemptType {
  org_id: string;
  quiz_id: string;
  user_id: string;
  user_start_time: Date;
  quiz_attempt_id?: string;
}
export interface startQuizResponseType {
  status?: string;
  quiz_attempt_id?: string;
}
export interface submitAnswerType {
  question_id: string;
  question_with_options: string;
  response_summary: string[];
  selected_answer_ids: string[];
}
export interface submitQuizType {
  org_id?: string;
  quiz_id?: string;
  user_id?: string;
  quiz_attempt_id?: string;
  submit_datas?: submitAnswerType[];
}

export interface examAnswerType {
  slot: number;
  answer: string;
  org_id: string;
  fraction: number;
  answer_id: string;
  ans_format: string;
  created_at: string;
  updated_at: string;
  answer_type: string;
  question_id: string;
  ansMarked?: boolean;
}
export interface examReviewType {
  name: string;
  duration: number;
  end_time: string;
  pass_mark: number;
  main_topic: string;
  start_time: string;
  total_mark: number;
  description: string;
  scored_mark: string;
  num_of_questions: number;
  skipped_ans_count: number;
  wrong_answer_count: number;
  correct_answer_count: number;
  quest_answers?: questAnswersType[];
}
export interface questAnswersType {
  mark: number;
  name: string;
  penalty: number;
  quiz_id: string;
  question_id: string;
  default_mark: number;
  question_slot: number;
  question_text: string;
  question_type: string;
  answers: reviewAnswersType[];
  selected_answer_ids: string[];
}
export interface reviewAnswersType {
  slot: number;
  answer: string;
  org_id: string;
  fraction: number;
  answer_id: string;
  ans_format: string;
  created_at: string;
  updated_at: string;
  answer_type: string;
  question_id: string;
  is_correct_answer: boolean;
}
export interface cleanExamResponseType {
  status: string;
  deleted_quiz_attempt_ids: string[];
}
export interface submitQuizResponseType {
  status?: string;
}
export interface SessionDetails {
  user_name?: string;
  enrolled_on?: string;
  last_watched_on?: string;
  checkpoint_name?: string;
  exam_attended_on?: string;
  exam_result?: string;
  video_name?: string;
}
export interface SessionDetailsColumnDefinition {
  column: Column<SessionDetails>;
}
export interface SessionDetailsRowDefinition {
  row: Row<SessionDetails>;
}
export interface UserSessionColumnDefinition {
  column: Column<CheckpointsUserStatsReturnType>;
}
export interface UserSessionRowDefinition {
  row: Row<CheckpointsUserStatsReturnType>;
}
export interface SessionDetailsProps {
  closeDialog: (value: boolean) => void;
  courseid: string;
  courseTitle: string;
  filterType: string;
  filterTypeLabel: string;
}
export interface CertificateProps {
  certificateData: CourseProgressResult;
}
export interface SignOutResponse {
  status: boolean;
  message: string;
}

export interface OrgSelection {
  orgList: ComboData[];
  closeDialog: () => void;
}
export interface CheckPointItem {
  org_id: string;
  sequence: number;
  created_at: string;
  created_by: string;
  start_time: string;
  updated_at: string;
  updated_by: string;
  instance_id: string;
  module_name: string;
  is_mandatory: boolean;
  checkpoint_id: string;
  module_type_id: string;
  checkpoint_name: string;
  checkpoint_type: string;
  course_module_id: string;
  instance_end_time: string;
  start_page: string;
}
export interface CheckPointsResponse {
  status: string;
  check_points: CheckPointItem[];
}

export interface Milestone {
  time: number;
  label: string;
}
export interface ResourceColumnDefinition {
  column: Column<ResourceForm>;
}
export interface ResourceRowDefinition {
  row: Row<ResourceForm>;
}
export interface coursePlanColumnDefinition {
  column: Column<CourseDatas>;
}
export interface coursePlanRowDefinition {
  row: Row<CourseDatas>;
}
export interface ResourceForm {
  course_name?: string;
  course_id?: string;
  course_module_id?: string;
  module_id?: string;
  instance?: string;
  section_id?: string;
  module_type?: string;
  module_name?: string;
  module_source?: string;
  isLinked?: boolean;
  full_name?: string;
  is_part_of_plan?: boolean;
  id?: string;
}

export interface MembershipPlan {
  planId: string;
  name: string;
  description: richTextType | string;
  basedOn: string;
  subscription_type: string;
  planFrequency: string;
  validFrom: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  validTo: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
  };
  amount: string;
  id?: string;
  is_subscription_assigned?: boolean;
}

export interface UserPlanList {
  result: UserPlanListResult[];
  status: string;
}
export interface UserPlanListRemoveUserId {
  user_id: string;
}
export interface UserPlanListResult {
  name: string;
  email: string;
  user_id: string;
  phone_number: string;
  payment_method: string;
  payment_status: string;
  user_purchase_date: string;
  subscription_amount: number;
  subscription_status: string;
  subscription_valid_to: string;
  subscription_plan_name: string;
  subscription_valid_from: string;
  user_subscription_end_date?: string;
}

export interface PendingSubscriptionResponse {
  result: {
    valid_to: string;
    valid_from: string;
    subscription_name: string;
    user_list: {
      email: string;
      amount: number;
      user_id: string;
      user_name: string;
      purchase_date: string;
      payment_method: string;
    }[];
  };
  status: string;
}

export interface SubscriptionValidity {
  id: string;
  valid_from: string;
  valid_to: string;
}

export interface PendingSubscription {
  email: string;
  amount: number;
  user_id: string;
  user_name: string;
  purchase_date: string;
  payment_method: string;
}
[];

export interface AddPendingSubscriptions {
  org_id: string;
  plan_id: string;
  user_ids: string[];
}

export interface AddPendingSubscriptionsResponse {
  status: string;
}
export interface DeleteSubscriptionResponse {
  status: string;
  code?: string;
  details?: string;
  hint?: string;
  message?: string;
}

export interface PendingSubscriptionRequest {
  org_id: string;
  plan_id: string;
}

export interface DeleteMembershipRequest {
  plan_id?: string;
  org_id?: string;
}
export interface ApproveMembershipRequest {
  plan_id?: string;
  org_id?: string;
}

export interface MembershipResponse {
  status: string;
}
export interface ApproveMembershipResponse {
  status: string;
}
export interface DeleteUserResponse {
  status: string;
}

export interface SignupErrorResponse {
  code: number;
  msg: string;
}
export interface profileImageRequest {
  id: string;
}
export interface profileImageResponse {
  avatar_url?: string;
}
export interface GetPrivilegeResponse {
  org_id: string;
  status: string;
  role_id: string;
  role_name: string;
  role_privileges: [];
}
export interface getRolePrivilegeList {
  org_id: string;
  status: string;
  user_id: string;
  user_roles: string[];
  role_privileges: RolePrivileges[];
}
export interface RolePrivileges {
  actions: Record<string, boolean>;
  screen: string;
}

export interface CommentRequest {
  instance_id?: string;
  org_id: string;
}

export interface CommentsListRequest {
  org_id: string;
}

export interface CommentResponse {
  id: string;
  name: string;
  type: string;
  message: string;
  subject: string;
  children: string;
  avatar_url: string;
  created_at: string;
  status?: string;
  approveIcon?: boolean;
  rejectIcon?: boolean;
  instance?: string;
  role_name?: string;
  resource_name?: string;
}
[];

export interface UpdateCommentStatusType {
  org_id: string;
  comment_id: string;
  status: string;
}

export interface CommentsRowDefinition {
  row: Row<CommentResponse>;
}
export interface SubscriptionList {
  id: string;
  org_id: string;
  name: string;
  description: string;
  subscription_type: string;
  target_id: string;
  price: number;
  currency: string;
  subscription_frequency_type: string;
  valid_from: string;
  valid_to: string;
  subscription_plan_status: string;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  is_expired: boolean;
}
[];
export interface SubscriptionResponseType {
  id?: string;
  status?: string;
  update_type?: string;
}
export interface SubscriptionRequestType {
  org_id: string;
  plan_id?: string;
  course_id?: string;
  mapping_type?: string;
  subscription_plan_data: {
    name?: string;
    description?: string;
    type?: string;
    price?: string;
    currency?: string;
    subscription_frequency?: string;
    valid_from?: string;
    valid_to?: string;
    status?: string;
  };
  courses?: CourseIdList[];
  course_module_ids?: string[];
}
export interface CourseIdList {
  course_id: string;
  is_expired: boolean;
}
[];
export interface SubscriptionPlans {
  is_subscription_assigned?: boolean;
  id: string;
  org_id: string;
  name: string;
  description: string;
  subscription_type: string;
  target_id: string;
  price: string;
  currency: string;
  subscription_frequency_type: string;
  subscription_plan_status: string;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  valid_from: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
    second?: number;
  };
  valid_to: {
    year: number;
    month: number;
    day: number;
    hour?: number | undefined;
    minute?: number;
    second?: number;
  };
}
[];
export interface SubscriptionCourse {
  status: string;
  courses_data?: CourseDatas[];
}
export interface CourseDatas {
  id: string;
  full_name: string;
  short_name: string;
  is_part_of_plan: boolean;
  course_name: string;
  name?: string;
  course_module_id?: string;
  is_expired: boolean;
  disableExpired?: boolean;
}
[];
export interface SubscriptionResource {
  status: string;
  resources_data?: CourseDatas[];
}
export interface Resources {
  resource_type: string;
  created_on: string;
  linked_course: string;
  linked_resource: string;
  status: string;
  resource_name: string;
}
export interface ResourcesFormDefinition {
  column: Column<ResourceList>;
}
export interface ResourcesFormRowDefinition {
  row: Row<ResourceList>;
}
export interface ActivityLogs {
  date_time: string;
  activity_task: string;
  task_details: string;
  activity_status: string;
  comments: string;
}
[];
export interface TreeTableItem {
  data: string | null;
  key?: string;
  value: string;
  label: string;
  description?: string;
  icon?: LucideIcon;
  children?: TreeTableItem[] | null;
  is_premium?: boolean;
  is_parent?: boolean;
  publish_status?: string;
}
export interface ResourceList {
  hideEdit?: boolean;
  hideViews?: boolean;
  hideEditDelete?: boolean;
  hideIcon?: boolean;
  id: string;
  name: string;
  content?: string;
  is_linked: number;
  created_at: string;
  file_type?: string;
  status?: string;
  external_url: string;
  order?: number;
  folder_name: string;
  is_watched?: boolean;
  thumbnail_url?: string;
  module_source?: string;
  duration?: number;
  folder_id?: string;
  pass_mark?: number;
  quiz_type?: string;
  main_topic?: string;
  total_mark?: number;
  description?: string;
  allowed_attempts?: number;
  num_of_questions?: number;
  penalty_available?: boolean;
  penalty_type?: string;
  calculation_type?: string;
  is_equal_weightage?: boolean;
  eq_weightage_marks?: number;
  minus_mark_applicable?: number;
  no_wrong_answers?: number;
}
[];

export interface CourseResources {
  status: string;
  resource_list: {
    course_id: string;
    full_name: string;
    short_name: string;
    section_name: string;
    course_module_id: string;
  }[];
}

export interface CourseResourceList {
  course_id: string;
  full_name: string;
  short_name: string;
  section_name: string;
  course_module_id: string;
}
[];

export interface CourseResourcesRequest {
  org_id: string;
  instance: string;
}
export interface ResourceLibraryRequest {
  org_id: string;
  module_id: string;
  linked: string;
  limit_val: number;
  offset_val: number;
}
export interface ResourceLibrary {
  status: string;
  total_count?: number;
  resource_list: ResourceList[];
}
export interface ResourceModules {
  name: string;
  description: string;
  creaated_at: string;
}

export interface moduleList {
  id: string;
  name: string;
  display_name: string;
}
[];
export interface courseNotLinkedRequest {
  org_id: string;
  module_id: string;
  course_id: string;
  section_id: string;
}
export interface courseLinkedRequest {
  resource_data: resourceData[];
  org_id: string;
  course_id: string;
  section_id: string;
  module_id: string;
}
export interface resourceData {
  instance_id: string;
  is_premium: boolean;
  folder_id?: string | null;
  module_order: number;
  module_id?: string;
  resource_id?: string;
}
[];
export interface deleteResourceRequest {
  org_id: string;
  instance_id: string;
}
export interface deleteResourceResponse {
  status: string;
}
export interface ModuleList {
  id: string;
  name: string;
  display_name: string;
}

export interface AddResourceFile {
  file_data: {
    url: string;
    name: string;
    description: string;
    module_source: string;
    extension?: string;
    page_count?: number;
    thumbnail_url?: string;
  };
  org_id: string;
}

export interface AddResourcePage {
  page_data: {
    name: string;
    content: richTextType | string;
    extension?: string;
  };
  org_id: string;
}

export interface AddResourceUrl {
  url_data: {
    name: string;
    description: string;
    external_url: string;
    module_source: string;
    length: string;
    extension?: string;
    page_count?: number;
    thumbnail_url?: string;
  };
  org_id: string;
}

export interface AddResourceResponse {
  status: string;
  instance_id: string;
}
export interface subscriptionListRequest {
  org_id: string;
  user_id?: string;
  limit_param: null;
  offset_param: number;
}
export interface subscriptionDeleteUserRequest {
  org_id: string;
  user_ids?: string[];
  plan_id?: string;
}
export interface SubscriptionListUpdated {
  result: SubscriptionListResults[];
  status: string;
  is_super_user: boolean;
  total_records: number;
}
export interface SubscriptionListResults {
  isCourseAssigned: boolean;
  disableExpired: boolean;
  hideExtendExpiry: boolean;
  id: string;
  name: string;
  price: 100;
  org_id: string;
  currency: string;
  valid_to: string;
  target_id: string;
  created_at: string;
  created_by: string;
  is_expired: boolean;
  updated_at: string;
  updated_by: string;
  valid_from: string;
  description: string;
  is_plan_approved: boolean;
  subscription_type: string;
  is_subscription_assigned: boolean;
  subscription_plan_status: string;
  subscription_frequency_type: string;
  subscription_status: string;
  hideViews?: boolean | string;
  hideEdit?: boolean;
  hideEditDelete?: boolean;
  hideIcon?: boolean;
  hideStatus?: boolean;
  is_subscription_course_assigned?: boolean;
  disableLinked?: boolean;
}
export interface DeleteResource {
  org_id: string;
  module_id: string;
  instance_id: string;
}
export interface ApproveResource {
  org_id: string;
  module_id: string;
  instance_id: string;
  status: string;
}
export interface DeleteResourceResponse {
  status: string;
}
export interface ApproveResourceResponse {
  status: string;
}

export interface ResourceLibraryData {
  id: string;
  url: string;
  name: string;
  status: string;
  is_linked: number;
  created_at: string;
  description: string;
  file_type: string;
  external_url: string;
  content?: string;
  length: string;
  module_source: string;
  extension?: string;
  page_count?: number;
  thumbnail_url?: string;
  folder_id: string;
}

export interface EditResourceFile {
  resource_data: {
    url: string;
    name: string;
    description: string;
    module_source: string;
    extension: string;
    page_count?: number;
    thumbnail_url?: string;
  };
  org_id: string;
  instance: string;
}

export interface EditResourcePage {
  resource_data: {
    name: string;
    content: richTextType | string;
    extension: string;
  };
  org_id: string;
  instance: string;
}

export interface EditResourceUrl {
  resource_data: {
    name: string;
    description: string;
    external_url: string;
    module_source: string;
    length: string;
    extension: string;
    page_count?: number;
    thumbnail_url: string;
  };
  org_id: string;
  instance: string;
}

export interface EditResourceResponse {
  status: string;
}

export interface DeleteCourseLinkedResources {
  course_module_id: string;
  org_id: string;
  course_id: string;
}
export interface CourseDetailsRequest {
  course_id: string;
  org_id: string;
}

export interface DeleteCategory {
  category_id: string;
  org_id: string;
}

export interface PublishCategory {
  category_id: string;
  org_id: string;
  status: string;
}

export interface PublishExam {
  quiz_id: string;
  status: string;
}

export interface PublishCategory {
  category_id: string;
  org_id: string;
  status: string;
}

export interface CategoryList {
  value: string;
  label: string;
  description: string;
  label_description?: string;
  publish_status?: string;
}

export interface CategoryListColumnDefinition {
  column: Column<CategoryList>;
}
export interface CategoryListRowDefinition {
  row: Row<CategoryList>;
}

export interface QuestionCategoryForm {
  value: string;
  label: string;
  description: string;
  label_description?: string;
  publish_status?: string;
}

export interface EditQuestionCategory {
  org_id: string;
  question_category_data: {
    id: string;
    name: string;
    description: string;
    // label_description:string;
  };
}
export interface ActivityLogRequest {
  org_id: string;
  user_id: string;
  limit_val: number;
  offset_val: number;
}
export interface ActivityLogResponse {
  result: UserLogList[];
  status: string;
}
export interface UserLogList {
  activity_name: string;
  activity_type: string;
  activity_user_name: string;
  comment: string;
  created_at: string;
  created_by: string;
  org_id: string;
  org_name: string;
  status_comment: string;
  target_type: string;
  target_id: string;
  target_data: {
    id: string;
    name: string;
  };
  user_agent: string;
}

export interface EditQuestionCategoryResponse {
  status: string;
  question_category_id: string;
}

export interface DeleteQuestionCategory {
  org_id: string;
  question_category_id: string;
}

export interface PublishQuestionCategory {
  org_id: string;
  ques_category_id: string;
  status: string;
}

export interface PublishQuestion {
  org_id: string;
  question_id: string | undefined;
  status: string;
}
export interface PublishQuestions {
  org_id: string;
  question_ids: (string | undefined)[];
  status: string;
}

export interface SuccessMessage {
  status: string;
}
[];

export interface AddFolderRequest {
  folder_data: {
    course_id: string;
    section_id: string;
    folder_name: string;
    description: string;
  };
  org_id: string;
}
export interface AddFolderResponse {
  status: string;
  folder_id: string;
}
export interface folderListResponse {
  id: string;
  folder_name: string;
  course_id: string;
  section_id: string;
  org_id: string;
  folder_id: string;
}
[];
export interface VideoProgresslistReturnType {
  status: string;
  result: StatisticList[];
}

export interface StatisticList {
  avatar_url: string;
  current_page: number;
  current_point: string;
  email: string;
  first_name: string;
  last_name: string;
  progress: number;
  status: string;
  time_spent: string;
  user_id: string;
  resources_details: ResourceStatisticData[];
}

export interface ResourceStatisticData {
  progress?: number;
  time_spent?: string;
  resource_id?: string;
  current_page?: number;
  current_point?: string;
  resource_name?: string;
}
export interface ProgressList {
  video_name: string;
  course_module_id: string;
  progress_user_list: UserProgressList[];
}
export interface UserProgressList {
  email: string;
  status: string;
  user_id: string;
  progress: number;
  last_name: string;
  first_name: string;
  avatar_url: string;
  time_spent: string;
  current_page?: number;
  current_point?: number;
  Resources_details: ResourceProgressList[];
}
export interface ResourceProgressList {
  progress: number;
  time_spent?: string;
  resource_id?: string;
  current_page: number;
  current_point?: string;
  resource_name: string;
}
export interface VideoProgressListRequestType {
  org_id: string;
  course_id: string;
  course_module_id: string | null;
}
export interface LatestEnrollmentResponse {
  id: string;
  first_name: string;
  last_name: string;
  enrolled_date: string;
  course_short_name: string;
  course_full_name: string;
  course_start_date: string;
  course_end_date: string;
}
export interface LatestEnrollmentColumnDefinition {
  column: Column<LatestEnrollmentResponse>;
}
export interface LatestEnrollmentRowDefinition {
  row: Row<LatestEnrollmentResponse>;
}
export interface AnswerType {
  type: string;
  isAnswer: boolean;
  fraction: number;
  text: string;
  id?: string;
  slot?: number;
}
[];
export interface DeleteQuestionRequest {
  question_id: string;
  org_id: string;
  quiz_id: string;
  course_id: string;
}

export interface ConfigurationRequest {
  config_env: string;
  org_id: string;
}

export interface ConfigurationResponse {
  result: {
    org_id: string;
    configs: [
      {
        valid_from: string;
        valid_upto: string;
        config_name: string;
        config_typ: string;
        config_value: string;
        config_set_name: string;
      },
    ];
    org_name: string;
  };
  status: string;
}

export interface Topics {
  org_id: string;
  filter_data?: number;
}

export interface UpdateBulletinRequest {
  bulletin_data: {
    id: string;
    course_id: null;
    content: string;
    title: string;
    type: string;
    publish_date: MomentInput;
    img_url: string;
    month: string;
  };
  org_id: string;
}

export interface UpdateBulletinResponse {
  status: string;
  bulletin_id: string;
  news_id?: string;
}

export interface DeleteBulletinRequest {
  bulletin_id: string;
  org_id: string;
}

export interface DeleteBulletinResponse {
  status: string;
}
export interface Notification {
  message: string;
}

export interface InviteUser {
  email: string;
}
export interface AddUserToSubscriptionRequest {
  org_id?: string;
  plan_id?: string;
  user_ids?: string[];
  subscription_plan_for_user_data: {
    purchase_date: string;
  };
}

export interface fetchTokenRequest {
  org_id?: string;
  user_id?: string;
}

export interface fetchTokenResponse {
  result: string[];
  status: string;
}
export interface pushNotificationRequest {
  userid: string;
  message: string;
  fcmtoken: string;
}
export interface pushNotificationResponse {
  userid: string;
  message: string;
  fcmtoken: string;
}

export interface insertMessageRequest {
  org_id: string;
  user_id: string;
  notification_data: {
    target_id: null;
    device_token_id: string;
    message_text: string;
  };
}
export interface insertMessageResponse {
  org_id: string;
  user_id: string;
  notification_data: {
    target_id: string;
    device_token_id: string;
    message_text: string;
  };
}
export interface EmailRequest {
  message: string;
  subject?: string;
  file?: unknown;
}
export interface SentEmailRequest {
  email: string;
  mail_content?: string;
  subject?: string;
  file?: unknown;
}
export interface SentEmailResponse {
  status: string;
  message: string;
}

export interface InviteUserRequest {
  recipient: string;
}
export interface InviteUserResponse {
  status: string;
  message: string;
}

export interface CourseProgressRequest {
  org_id?: string;
  course_id?: string;
  user_id?: string | null;
}
export interface CourseProgressResult {
  user_id: string;
  user_name: string;
  time_spent: string;
  user_email: string;
  attended_at: string;
  course_name: string;
  current_page: number;
  current_time: string;
  total_length: string;
  resource_name: string;
  total_progress: number;
  progress_percent: number;
  hideIcon: boolean;
  avatar_url?: string;
  resources: ResourceProgress[];
  resource_count: number;
}

export interface ResourceProgress {
  time_spent: string;
  attended_at: string;
  current_page: string;
  current_time: string;
  total_length: string;
  resource_name: string;
  progress_percent: number;
}
[];
export interface CourseProgressResponse {
  result: CourseProgressResult[];
  status: string;
}
export interface CoursePlanResponse {
  status: string;
  result: CoursePlanResult[];
}
[];

export interface CoursePlanResult {
  currency: string;
  name: string;
  org_id: string;
  price: number;
  status: string;
  subscription_frequency: string;
  subscription_plan_id: string;
  subscription_type: string;
  valid_from: string;
  valid_to: string;
}
[];
export interface BreadcrumbItem {
  screen_name: string;
  child_screens: InnerItem;
}

export interface InnerItem {
  name: string;
  path: string;
}
export interface EnrolledCourseResponse {
  course_id: string;
  course_name: string;
  course_type: string;
  duration: number;
  is_part_of_plan: boolean;
  status: string;
  subscription_valid_to: string;
  summary: string;
  valid_to: string;
}
export interface EnrolledCourseColumnDefinition {
  column: Column<EnrolledCourseResponse>;
}
export interface EnrolledCourseRowDefinition {
  row: Row<EnrolledCourseResponse>;
}

export interface GetCourseByCategoryReq {
  org_id: string;
  category_id: string;
}

export interface GetCourseByCategoryResponse {
  id: string;
  courses: CourseParams[];
  category_name: string;
  subcategories?: Subcategory[];
}

export interface Subcategory {
  courses?: CourseParams[];
  sub_category_id: string;
  sub_category_name: string;
}

export interface ReportersResponse {
  id: string;
  role: string;
  last_name: string;
  first_name: string;
}
[];
export interface UsersNotEnrolled {
  avatar_url: string;
  email: string;
  first_name: string;
  id: string;
  last_name: string;
  phonenumber1: string;
}
[];
export interface CourseCompletedUsers {
  course_id: string;
  org_id: string;
  user_id: string;
}
export interface ExportExcelDetails {
  Name: string;
  Email: string;
  Course: string;
  Attended_on: Date;
}

export interface ExcelImportQuestion {
  org_id?: string;
  slNo?: number;
  qid?: number;
  ques_category_id: string;
  input_data: {
    questions: {
      question_text: string;
      question_hint: string;
      options: {
        option1: string;
        option2: string;
        option3: string;
        option4: string;
        option5: string;
      };
      right_answer: string[];
      solutions: {
        solution1: string;
        solution2: string;
        solution3: string;
        solution4: string;
        solution5: string;
      };
      best_solutions: {
        best_solution1: string;
        best_solution2: string;
        best_solution3: string;
      };
    }[];
  };
  isQuestionAdded?: boolean;
}

export interface ExcelQuestionResponse {
  status: string;
  question_ids: {
    question_id: string;
  }[];
  message?: string;
}

export interface AddFolderFromLibraryRequest {
  validity_from: string;
  validity_to: string;
  folder_name: string;
  description: string;
  status: string;
  org_id: string;
}

export interface ListFolderFromLibraryRequest {
  org_id: string;
}

export interface DeleteSectionRequest {
  course_id: string;
  org_id: string;
  section_id: string;
}

export interface folderResorcesResponse {
  org_id: string;
  folder_id: string;
}

export interface FolderResourecList {
  created_at: string;
  is_linked: boolean;
  module_id: string;
  resource_id: string;
  resource_name: string;
  order: number;
  module_type: string;
}
[];
export interface ImportFolderRequest {
  org_id: string;
  course_id: string;
  section_id: string;
  resource_data: ResourceDataRequest[];
}
export interface ResourceDataRequest {
  module_id: string;
  resource_id: string;
  module_order: number;
}
[];
export interface ExtendPlanValidityRequest {
  org_id: string;
  plan_id: string;
  validity_upto_date: string;
}
export interface GetCourseListRequest {
  org_id: string;
  resource_id: string;
}
export interface GetCourseListResponse {
  status: string;
  courses_data: CourseMpaData[];
}
export interface CourseMpaData {
  id: string;
  valid_to: string;
  full_name: string;
  short_name: string;
  valid_from: string;
  section_id?: string;
  is_part_of_plan: boolean;
  sections: {
    id: string;
    name: string;
  }[];
}
[];
export interface MapResourcesToCourseRequest {
  resource_id: string;
  org_id: string;
  module_type: string;
  course_and_section: {
    course_id: string;
    section_id: string;
  }[];
}

export interface UpdateResourceUrlRequest {
  org_id: string;
  module_type: string;
  resource_url: string | null;
  video_length: string | null;
  thumbnail_url: string | null;
  extension: string | null;
  page_count: number | null;
  resource_id: string | null;
  page_content: string | null;
}

export interface ExcelImportQuestion {
  org_id?: string;
  slNo?: number;
  qid?: number;
  ques_category_id: string;
  input_data: {
    questions: {
      question_text: string;
      question_hint: string;
      options: {
        option1: string;
        option2: string;
        option3: string;
        option4: string;
        option5: string;
      };
      right_answer: string[];
      solutions: {
        solution1: string;
        solution2: string;
        solution3: string;
        solution4: string;
        solution5: string;
      };
      best_solutions: {
        best_solution1: string;
        best_solution2: string;
        best_solution3: string;
      };
    }[];
  };
  isQuestionAdded?: boolean;
}

export interface ExcelQuestionResponse {
  status: string;
  question_ids: {
    question_id: string;
  }[];
}

export interface CustomBrandingDetails {
  org_id: string;
  theme_name: string;
  main_logo: string;
  app_logo: string;
  banner_image: string;
  app_background_color: string;
  // top_bar_color: string;
  font_family: string;
  button_primary_color: string;
  button_primary_text_color: string;
  button_secondary_color: string;
  button_secondary_text_color: string;
  button_dismiss_bg_color: string;
  button_dismiss_text_color: string;
  button_info_background_color: string;
  button_info_text_color: string;
  toast_success_color: string;
  toast_error_color: string;
  toast_warning_color: string;
  toast_info_color: string;
  navigation_text_color?: string;
  footer_background_color: string;
  footer_text_color: string;
  footer_text: string;
  welcome_text: string;
  valid_from: string;
  valid_to: string;
  top_bar_text_color: string;
  top_bar_background_color: string;
  top_bar_active_color: string;
  navbar_background_color: string;
  navbar_text_color: string;
  navbar_text_color_hover: string;
  sidebar_background_color: string;
  sidebar_text_color: string;
  sidebar_active_color: string;
  sidebar_active_background_color: string;
  favicon: string;
  font_color: string;
  font_base_size: string;
}
[];
export interface DeleteCustomResource {
  org_id: string;
  resource_id: string;
  // component_type: string;
}

export interface APIResponse {
  status: string;
  message: string;
}

export interface UpdateCustomResource {
  org_id: string;
  status?: string;
  course_id: string | null;
  assignments: {
    Assignments: string[];
    no_of_items: number;
    component_type: string;
  };
  general_resources: {
    resources: CustomResource[];
    no_of_items: number;
    component_type: string;
  };
  current_affairs: {
    no_of_items: number;
    component_type: string;
  };
  subjects: {
    subjects?: {
      id?: string;
      name?: string;
    }[];
    no_of_items: number;
    component_type: string;
  };
  exams: {
    show_exam: number;
  };
}

export interface CustomDashboardData {
  org_id: string;
  course_id: string | null;
}

export interface CustomDashboardResponse {
  data: UpdateCustomResource;
  status: string;
}

export interface CustomResource {
  resource_id: string;
  resource_name: string;
  thumbnail_url: string;
  resource_type: string;
  file_extension?: string;
  isResourceAdded?: boolean;
  section_name?: string;
  resource_order?: number;
}
export interface CustomResourceRowDefinition {
  row: Row<CustomResource>;
}
export interface AllResourceRequest {
  org_id: string;
  course_id: string;
  user_id: null;
}

export interface AllResourceData {
  quiz_type: string;
  course_id: string;
  course_name: string;
  course_short_name: string;
  external_url: string;
  file_extension: string;
  is_course_expired: boolean;
  is_part_of_plan: boolean;
  page_count: string | number;
  resource_id: string;
  resource_name: string;
  resource_type: string;
  resource_url: string;
  summary: string;
  valid_to: string;
  video_length: string;
  thumbnail_url?: string;
  is_checkpoint_enabled: boolean;
  section_order: number;
  module_order: number;
  progress: number;
  course_module_id: string;
  resource_status: number;
  section_name: string;
}

export interface AllResourceResponse {
  result: AllResourceData[];
  status: string;
}
export interface PublishMeetingDetailsReq {
  org_id: string;
  course_id: string | null;
  meeting_id: string | null;
  meeting_url: string | null;
  start_date: string;
  end_date: string;
}
export interface PublishMeetingResponse {
  status: string;
  live_class_id: string;
}

export interface AddCommentRequest {
  comment_data: {
    subject: string;
    message: string;
    type: string;
    parent_id?: string;
    activity_type: string;
  };
  instance_id: string;
  user_id: string;
}

export interface AddCommentResponse {
  status: string;
  comment_id: string;
}

export interface CourseListRequest {
  org_id: string;
  exam_id: string;
}
export interface CourseListResponse {
  valid_to: string;
  course_id: string;
  full_name: string;
  short_name: string;
  valid_from: string;
  sections: {
    name: string;
    section_id: string;
    section_order?: number;
    folderDtls?: FolderData[];
  }[];
}

export interface FolderData {
  folder_id: string;
  folder_name: string;
}

export interface QuizToCourseRequest {
  org_id: string;
  exam_id: string;
  course_and_section: {
    course_id: string;
    section_id: string;
  }[];
}
export interface QuizToCourseResponse {
  status: string;
  message?: string;
}

export interface LogUserActivityRequest {
  activity_type: string;
  screen_name: string;
  action_details: string;
  target_id: string;
  log_result: string;
  org_id?: string;
  user_id?: string;
}

export interface LogUserActivityResponse {
  status: string;
  activity_log_id: string;
}

export interface ImportExamRequest {
  org_id: string;
  quiz_data: {
    quiz_id?: string;
    name: string;
    description: string;
    main_topic: string;
    num_of_questions: number;
    total_mark: number;
    pass_mark: number;
    duration: number;
    penalty_available: boolean;
    allowed_attempts: number;
    quiz_type: string;
    is_premium: boolean;
    is_deleted: boolean;
    status: string;
    is_equal_weightage: boolean;
    eq_weightage_marks: number;
    folder_id: string;
    no_wrong_answers: number;
    minus_mark_applicable: number;
    parent_quiz_id: string | null;
    penalty_type: string;
    calculation_type: string;
  };
  folder_id: string;
}
export interface ImportExamResponse {
  status: string;
  instance_id: string;
}

export interface LinkExamToCourse {
  course_id: string;
  section_id?: string;
  org_id: string;
  folder_id: string | null;
  quiz_data: {
    name: string;
    description: string;
    main_topic: string;
    num_of_questions: number;
    total_mark: number;
    pass_mark: number;
    start_time: string | null;
    end_time: string | null;
    duration: number;
    allowed_attempts: number;
    quiz_type: string;
    penalty_available: boolean;
    penalty_type: string;
    calculation_type: string;
    is_equal_weightage: boolean;
    eq_weightage_marks: number;
    status: string;
    minus_mark_applicable: number;
    no_wrong_answers: number;
  };
}

export interface AddSectionRequest {
  org_id: string;
  course_id: string;
  section_details: {
    name: string;
    section_order: number;
  }[];
}

export interface AddSectionResponse {
  status: string;
  section_ids: string[];
}
export interface YouTubeVideoDetails {
  items: {
    contentDetails: {
      duration: string; // ISO 8601 duration like "PT1H2M3S"
    };
  }[];
}
export interface ManageFolderRequest {
  folder_id: string;
  folder_name: string;
}

export interface OrderSectionRequest {
  org_id: string;
  course_id: string;
  sections: {
    section_id: string;
    section_order: number;
  }[];
}
export interface sectionData {
  course_module_id: string;
  instance: string;
  module_id: string;
  module_name: string;
  module_order: number;
  module_type: string;
}
export interface ResourceItem {
  module_id: string;
  module_name: string;
  module_type: string;
  module_order: number;
  course_module_id: string;
  instance: string;
}

export interface ResourceReorderProps {
  isOpen: boolean;
  onClose: () => void;
  resources: ResourceItem[];
  sectionTitle: string;
  onSave: (reorderedResources: ResourceItem[]) => Promise<void>;
}
export interface OrderFolderRequest {
  org_id: string;
  course_id: string;
  section_id: string;
  folders: {
    folder_id: string;
    folder_order: number;
  }[];
}
export interface OrderResourceRequest {
  org_id: string;
  course_id: string;
  section_id: string;
  folder_id: string | null;
  resources: {
    resource_id: string;
    resource_order: number;
  }[];
}

export interface GetCoursePurchaseRequest {
  org_id: string;
  course_id?: string;
}

export interface CoursePurchase {
  status: string;
  requests: CoursePurchaseRequestResponse[];
}
export interface CoursePurchaseRequestResponse {
  id: string;
  user_id: string;
  first_name: string;
  last_name: string;
  user_name: string;
  email: string;
  course_id: string;
  course_name: string;
  requested_on: string;
  request_status: string;
  selected_plan_id: string;
  requested_by: string;
}

export interface UpdatePurchaseRequest {
  org_id: string;
  course_id: string;
  request_status: string;
  status_notes: string | null;
  requested_by: string;
}
export interface LiveClassRequest {
  org_id: string;
  course_id: string | null;
}
export interface LiveClassResponse {
  status: string;
  end_date: string;
  passcode: string;
  course_id: string;
  meeting_id: string;
  start_date: string;
  meeting_url: string;
  meeting_type: string;
  meeting_status: string;
  live_class_id: string;
}
[];
export interface LiveClassUpdateRequest {
  id: string;
  meeting_id: string;
  meeting_url: string;
  passcode: string;
  valid_from: string;
  valid_upto: string;
}
export interface DeleteExamRequest {
  exam_id: string;
  org_id: string;
}
export interface DeleteFolderRequest {
  folder_id: string;
  org_id: string;
  course_id: string;
  section_id: string;
}
export interface RenameFolderRequest {
  folder_id: string;
  org_id: string;
  course_id: string;
  section_id: string;
  folder_name: string;
}
