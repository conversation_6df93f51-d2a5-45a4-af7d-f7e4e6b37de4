"use client";
import React, { useEffect, useState } from "react";
import type {
  ComboData,
  ErrorCatch,
  LogUserActivityRequest,
  ResourceLibrary,
  ResourceList,
  ToastType,
  courseLinkedRequest,
  courseNotLinkedRequest,
  folderListResponse,
  moduleList,
  resourceData,
} from "@/types";
// import { DataTable } from "@/components/ui/data-table/data-table";
import { Button } from "@/components/ui/button";
import { DataTable } from "primereact/datatable";
import { Combobox } from "@/components/ui/combobox";
import useResourceLibrary from "@/hooks/useResourceLibrary";
import { useToast } from "@/components/ui/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "@/lib/messages";
import { Spinner } from "@/components/ui/progressiveLoader";
import useCourse from "@/hooks/useCourse";
import { Column } from "primereact/column";
import moment from "moment";
import "../../styles/main.css";
import useLogUserActivity from "@/hooks/useLogUserActivity";
import { useTranslation } from "react-i18next";

export default function ImportResource({
  onCancel,
  onSave,
  courseId,
  sectionsId,
  is_premium,
  moduleOrders,
  targetFolderId,
}: {
  onSave: () => void;
  onCancel: () => void;
  isModal?: boolean;
  courseId?: string;
  sectionsId?: string;
  is_premium?: string;
  moduleOrders?: number[];
  targetFolderId?: string;
}): React.JSX.Element {
  const { t } = useTranslation();
  const [resourceData, setResourceData] = useState<ResourceList[]>([]);
  const [selectedResourceData, setSelectedResourceData] = useState<
    ResourceList[]
  >([]);
  const [label, setLabel] = useState<string>("");
  const [moduleId, setModuleId] = useState<string>("");
  const [folderId, setfolderId] = useState<string>("");
  const [isPremium, setIsPremium] = useState<boolean>(false);
  const [comboResources, setComboResources] = useState<ComboData[]>([]);
  const [comboFolderData, setComboFolderData] = useState<ComboData[]>([]);
  const { getResourceNotLinkedList, updateResourceLinkedList } =
    useResourceLibrary();
  const { getFolderList } = useCourse();
  const { updateUserActivity } = useLogUserActivity();

  const { toast } = useToast() as ToastType;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [defaultPremium, setDefaultPremium] = useState<boolean>(false);
  const [rowSelection, setRowSelection] = useState({});
  const existingOrders: number[] = [];
  const [defaultOrder, setDefaultOrder] = useState<number>(1);
  useEffect(() => {
    const maxOrder = Math.max(...(moduleOrders ?? []));
    if (maxOrder > 0) {
      setDefaultOrder(maxOrder + 1);
    }
    if (is_premium == "true") {
      setIsPremium(true);
      setDefaultPremium(true);
    } else {
      setIsPremium(false);
      setDefaultPremium(false);
    }
    const moduleData = localStorage.getItem("moduleList") as string;
    const displayData = JSON.parse(moduleData) as moduleList[];
    const datas: ComboData[] = displayData.map((item: moduleList) => ({
      value: item.id,
      label: item.display_name,
    }));
    setComboResources(datas as ComboData[]);
    setModuleId(datas[0]?.value as string);
    setLabel(datas[0]?.label as string);
    getFoldersList();
    console.log(rowSelection);
  }, []);

    const updateLogUserActivity = async (
      params: LogUserActivityRequest,
    ): Promise<void> => {
      const response = await updateUserActivity(params);
      console.log("response", response);
    };

    
  const getFoldersList = (): void => {
    const fetchData = async (): Promise<void> => {
      try {
        const data = await getFolderList(
          courseId as string,
          sectionsId as string,
        );
        const datas: ComboData[] = data.map((item: folderListResponse) => ({
          value: item.id,
          label: item.folder_name,
        }));
        setComboFolderData(datas);
      } catch (error) {
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  useEffect(() => {
    const requestParams = {
      org_id: localStorage.getItem("orgId") as string,
      module_id: moduleId,
      course_id: courseId as string,
      section_id: sectionsId as string,
    };
    if (moduleId.length > 0) {
      getNotLinkedCourse(requestParams);
    }
  }, [moduleId]);
  useEffect(() => {
    // Whenever resourceData changes, reset the selection
    setSelectedResourceData([]);
    setRowSelection({});
  }, [resourceData, moduleId]);

  const getNotLinkedCourse = (params: courseNotLinkedRequest): void => {
    /* setIsLoading(true); */
    const fetchData = async (): Promise<void> => {
      try {
        const resource: ResourceLibrary = await getResourceNotLinkedList(
          params,
        );
        if (resource.resource_list != null) {
          setIsLoading(false);
          resource.resource_list
            .sort((item, data) => {
              return item.name.localeCompare(data.name);
            })
            .forEach((item, index) => {
              item.order = defaultOrder + index;
            });

          setResourceData(resource.resource_list);
        }
      } catch (error) {
        setIsLoading(false);
        const err = error as ErrorCatch;
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: err?.message,
        });
        console.error("Error fetching data:");
      }
    };
    fetchData().catch((error) => console.log(error));
  };
  const handleCancel = (): void => {
    onCancel();
  };
  const importResource = (): void => {
    if (selectedResourceData.length === 0) {
      toast({
        variant: ERROR_MESSAGES.toast_variant_destructive,
        title: t("errorMessages.toast_error_title"),
        description:t("errorMessages.not_select_resource"),
      });
    } else {
      // Track the order values to check for duplicates
      const orderValues: number[] = [];

      const hasDuplicates = selectedResourceData.some((item) => {
        if (moduleOrders?.includes(item.order as number) ?? false) {
          existingOrders.push(item.order as number); // Add duplicate order to the list
        }
        if (orderValues.includes(item.order as number)) {
          return true; // Duplicate found
        }
        orderValues.push(item.order as number); // Add the order to the list
        return false;
      });

      const resourcesWithoutOrder = selectedResourceData.filter(
        (item) =>
          item.order === undefined || item.order === null || item.order === 0,
      );

      if (resourcesWithoutOrder.length > 0) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: t("errorMessages.order_mandatory_alert"),
        });
        return;
      }
      if (existingOrders.length > 0) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description: `${t("errorMessages.order_exist")} ${existingOrders.join(
            ", ",
          )}`,
        });
        return;
      } else if (hasDuplicates) {
        toast({
          variant: ERROR_MESSAGES.toast_variant_destructive,
          title: t("errorMessages.toast_error_title"),
          description:t("errorMessages.duplicate_resource_alert"),
        });
        return;
      } else {
        const resources: resourceData[] = [];

        selectedResourceData.forEach((item) => {
          resources.push({
            is_premium: isPremium,
            instance_id: item.id as string,
            folder_id: targetFolderId ?? (folderId.length > 0 ? folderId : null),
            module_order: item.order as number,
          });
        });

        const reqParams: courseLinkedRequest = {
          resource_data: resources,
          module_id: moduleId,
          org_id: localStorage.getItem("orgId") as string,
          course_id: courseId as string,
          section_id: sectionsId as string,
          ...((targetFolderId != null) ? { folder_id: targetFolderId } : {}),
        };

        const fetchData = async (): Promise<void> => {
          try {
            const response = await updateResourceLinkedList(reqParams);
            if (response.status === "success") {
              toast({
                variant: SUCCESS_MESSAGES.toast_variant_default,
                title: t("successMessages.toast_success_title"),
                description: t("successMessages.import_resource"),
              });
              const params = {
                activity_type: "Course",
                screen_name: "Course",
                action_details: "Resource imported successfully",
                target_id: courseId as string,
                log_result: "SUCCESS",
              };
              void updateLogUserActivity(params).catch((error) => {
                console.error(error);
              });
              onSave();
            } else {
              const params = {
                activity_type: "Course",
                screen_name: "Course",
                action_details: "Failed to import resource",
                target_id: courseId as string,
                log_result: "ERROR",
              };
              void updateLogUserActivity(params).catch((error) => {
                console.error(error);
              });
            }
          } catch (error) {
            const err = error as ErrorCatch;
            toast({
              variant: ERROR_MESSAGES.toast_variant_destructive,
              title: t("errorMessages.toast_error_title"),
              description: err?.message,
            });
            console.error("Error fetching data:");
          }
        };
        fetchData().catch((error) => console.log(error));
      }
    }
  };

  const handleResourceChange = (value: string): void => {
    setModuleId(value);
  };
  const handleFolderChange = (value: string): void => {
    setfolderId(value);
  };
  const handleSelectedData = (arg: ResourceList[]): void => {
    // setRowSelection({});
    setSelectedResourceData(arg);
  };

  return (
    <div className="border rounded-md p-4 mt-4">
      <div className="w-full flex justify-between space-x-4">
        <div className="w-full flex items-center"></div>
      </div>
      <div className="flex md:mr-auto items-center mb-5">
        <div className="md:min-w-[430px]  mr-4">
          <Combobox
            data={comboResources}
            onSelectChange={handleResourceChange}
            placeHolder={String(t("courses.courseModule.selectResourceType"))}
            defaultLabel={label}
          />
        </div>
        <div className="flex md:mr-auto items-center">
          <div className="md:min-w-[430px]">
            <Combobox
              data={comboFolderData}
              onSelectChange={handleFolderChange}
              placeHolder={String(t("courses.courseModule.selectFolder"))}
            />
          </div>
        </div>
      </div>
      {isLoading ? (
        <Spinner />
      ) : (
        <div>
          <DataTable
            value={resourceData}
            selection={selectedResourceData}
            onSelectionChange={(e) => handleSelectedData(e.value)} // Ensure e.value is passed properly
            dataKey="id"
            paginator
            rows={10}
            selectionMode="multiple"
            className="p-datatable-striped border border-gray-300 rounded-lg bg-gray"
          >
            <Column
              selectionMode="multiple"
              headerStyle={{
                width: "2.5em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              className="text-sm text-gray-700 border-b border-gray-300 bg-white"
              bodyClassName="p-0.5 pl-2"
            />
            <Column
              field="index"
              header={String(t("courses.courseModule.slno"))}
              headerStyle={{
                width: "5em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              body={(data, options) => options.rowIndex + 1}
              className=" text-sm text-gray-700 border-b border-gray-300 p-0.5 bg-white"
            />
            <Column
              field="name"
              header={String(t("courses.courseModule.resourceName"))}
              headerStyle={{
                width: "12em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              className=" text-sm text-gray-700 border-b border-gray-300 p-0.5 bg-white"
            />
            <Column
              header={String(t("courses.courseModule.createdOn"))}
              headerStyle={{
                width: "10em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              body={(rowData: ResourceList) =>
                moment
                  .utc(rowData.created_at)
                  .local()
                  .format("DD-MMM-YYYY hh:mm a")
              }
              className=" text-sm text-gray-600 border-b border-gray-300 p-0.5 bg-white"
            />
            <Column
              header={String(t("courses.courseModule.order"))}
              headerStyle={{
                width: "8em",
                backgroundColor: "#00afbb",
                color: "white",
                padding: "0.5rem",
              }}
              body={(rowData: ResourceList, options) => (
                <input
                  type="number"
                  min="0"
                  defaultValue={defaultOrder + options.rowIndex}
                  onChange={(e) => {
                    const value = Math.max(0, Number(e.target.value));
                    rowData.order = value;
                  }}
                  className="w-12 border border-gray-300 p-0.5 rounded-sm text-red"
                  disabled={
                    !selectedResourceData.some(
                      (selected) => selected.id === rowData.id,
                    )
                  }
                />
              )}
              className="border-b border-gray-300 p-0.5 bg-white"
            />
          </DataTable>
        </div>
      )}

      <div className="flex flex-wrap justify-start mt-6">
        <label className="mr-2 font-medium">{String(t("courses.courseModule.isPremium"))}: </label>
        <div className="">
          <Checkbox
            checked={isPremium}
            onCheckedChange={(value: boolean) => {
              setIsPremium(value);
            }}
            disabled={defaultPremium}
          />
        </div>
      </div>
      <div className="flex flex-wrap justify-end ">
        <div className="sm:px-2 md:px-3 lg:px-4 xl:px-5 2xl:px-6">
          <Button
            variant="outline"
            className="w-full sm:w-auto"
            onClick={handleCancel}
          >
            {String(t("buttons.cancel"))}
          </Button>
        </div>
        <div>
          <Button onClick={importResource}>{String(t("buttons.submit"))}</Button>
        </div>
      </div>
    </div>
  );
}
